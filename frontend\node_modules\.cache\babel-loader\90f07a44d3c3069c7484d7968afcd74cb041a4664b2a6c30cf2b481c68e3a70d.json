{"ast": null, "code": "import { globalThisShim as globalThis } from \"./globals.node.js\";\nexport function pick(obj, ...attr) {\n  return attr.reduce((acc, k) => {\n    if (obj.hasOwnProperty(k)) {\n      acc[k] = obj[k];\n    }\n    return acc;\n  }, {});\n}\n// Keep a reference to the real timeout functions so they can be used when overridden\nconst NATIVE_SET_TIMEOUT = globalThis.setTimeout;\nconst NATIVE_CLEAR_TIMEOUT = globalThis.clearTimeout;\nexport function installTimerFunctions(obj, opts) {\n  if (opts.useNativeTimers) {\n    obj.setTimeoutFn = NATIVE_SET_TIMEOUT.bind(globalThis);\n    obj.clearTimeoutFn = NATIVE_CLEAR_TIMEOUT.bind(globalThis);\n  } else {\n    obj.setTimeoutFn = globalThis.setTimeout.bind(globalThis);\n    obj.clearTimeoutFn = globalThis.clearTimeout.bind(globalThis);\n  }\n}\n// base64 encoded buffers are about 33% bigger (https://en.wikipedia.org/wiki/Base64)\nconst BASE64_OVERHEAD = 1.33;\n// we could also have used `new Blob([obj]).size`, but it isn't supported in IE9\nexport function byteLength(obj) {\n  if (typeof obj === \"string\") {\n    return utf8Length(obj);\n  }\n  // arraybuffer or blob\n  return Math.ceil((obj.byteLength || obj.size) * BASE64_OVERHEAD);\n}\nfunction utf8Length(str) {\n  let c = 0,\n    length = 0;\n  for (let i = 0, l = str.length; i < l; i++) {\n    c = str.charCodeAt(i);\n    if (c < 0x80) {\n      length += 1;\n    } else if (c < 0x800) {\n      length += 2;\n    } else if (c < 0xd800 || c >= 0xe000) {\n      length += 3;\n    } else {\n      i++;\n      length += 4;\n    }\n  }\n  return length;\n}\n/**\n * Generates a random 8-characters string.\n */\nexport function randomString() {\n  return Date.now().toString(36).substring(3) + Math.random().toString(36).substring(2, 5);\n}", "map": {"version": 3, "names": ["globalThisShim", "globalThis", "pick", "obj", "attr", "reduce", "acc", "k", "hasOwnProperty", "NATIVE_SET_TIMEOUT", "setTimeout", "NATIVE_CLEAR_TIMEOUT", "clearTimeout", "installTimerFunctions", "opts", "useNativeTimers", "setTimeoutFn", "bind", "clearTimeoutFn", "BASE64_OVERHEAD", "byteLength", "utf8Length", "Math", "ceil", "size", "str", "c", "length", "i", "l", "charCodeAt", "randomString", "Date", "now", "toString", "substring", "random"], "sources": ["C:/restraunat managment system/frontend/node_modules/engine.io-client/build/esm/util.js"], "sourcesContent": ["import { globalThisShim as globalThis } from \"./globals.node.js\";\nexport function pick(obj, ...attr) {\n    return attr.reduce((acc, k) => {\n        if (obj.hasOwnProperty(k)) {\n            acc[k] = obj[k];\n        }\n        return acc;\n    }, {});\n}\n// Keep a reference to the real timeout functions so they can be used when overridden\nconst NATIVE_SET_TIMEOUT = globalThis.setTimeout;\nconst NATIVE_CLEAR_TIMEOUT = globalThis.clearTimeout;\nexport function installTimerFunctions(obj, opts) {\n    if (opts.useNativeTimers) {\n        obj.setTimeoutFn = NATIVE_SET_TIMEOUT.bind(globalThis);\n        obj.clearTimeoutFn = NATIVE_CLEAR_TIMEOUT.bind(globalThis);\n    }\n    else {\n        obj.setTimeoutFn = globalThis.setTimeout.bind(globalThis);\n        obj.clearTimeoutFn = globalThis.clearTimeout.bind(globalThis);\n    }\n}\n// base64 encoded buffers are about 33% bigger (https://en.wikipedia.org/wiki/Base64)\nconst BASE64_OVERHEAD = 1.33;\n// we could also have used `new Blob([obj]).size`, but it isn't supported in IE9\nexport function byteLength(obj) {\n    if (typeof obj === \"string\") {\n        return utf8Length(obj);\n    }\n    // arraybuffer or blob\n    return Math.ceil((obj.byteLength || obj.size) * BASE64_OVERHEAD);\n}\nfunction utf8Length(str) {\n    let c = 0, length = 0;\n    for (let i = 0, l = str.length; i < l; i++) {\n        c = str.charCodeAt(i);\n        if (c < 0x80) {\n            length += 1;\n        }\n        else if (c < 0x800) {\n            length += 2;\n        }\n        else if (c < 0xd800 || c >= 0xe000) {\n            length += 3;\n        }\n        else {\n            i++;\n            length += 4;\n        }\n    }\n    return length;\n}\n/**\n * Generates a random 8-characters string.\n */\nexport function randomString() {\n    return (Date.now().toString(36).substring(3) +\n        Math.random().toString(36).substring(2, 5));\n}\n"], "mappings": "AAAA,SAASA,cAAc,IAAIC,UAAU,QAAQ,mBAAmB;AAChE,OAAO,SAASC,IAAIA,CAACC,GAAG,EAAE,GAAGC,IAAI,EAAE;EAC/B,OAAOA,IAAI,CAACC,MAAM,CAAC,CAACC,GAAG,EAAEC,CAAC,KAAK;IAC3B,IAAIJ,GAAG,CAACK,cAAc,CAACD,CAAC,CAAC,EAAE;MACvBD,GAAG,CAACC,CAAC,CAAC,GAAGJ,GAAG,CAACI,CAAC,CAAC;IACnB;IACA,OAAOD,GAAG;EACd,CAAC,EAAE,CAAC,CAAC,CAAC;AACV;AACA;AACA,MAAMG,kBAAkB,GAAGR,UAAU,CAACS,UAAU;AAChD,MAAMC,oBAAoB,GAAGV,UAAU,CAACW,YAAY;AACpD,OAAO,SAASC,qBAAqBA,CAACV,GAAG,EAAEW,IAAI,EAAE;EAC7C,IAAIA,IAAI,CAACC,eAAe,EAAE;IACtBZ,GAAG,CAACa,YAAY,GAAGP,kBAAkB,CAACQ,IAAI,CAAChB,UAAU,CAAC;IACtDE,GAAG,CAACe,cAAc,GAAGP,oBAAoB,CAACM,IAAI,CAAChB,UAAU,CAAC;EAC9D,CAAC,MACI;IACDE,GAAG,CAACa,YAAY,GAAGf,UAAU,CAACS,UAAU,CAACO,IAAI,CAAChB,UAAU,CAAC;IACzDE,GAAG,CAACe,cAAc,GAAGjB,UAAU,CAACW,YAAY,CAACK,IAAI,CAAChB,UAAU,CAAC;EACjE;AACJ;AACA;AACA,MAAMkB,eAAe,GAAG,IAAI;AAC5B;AACA,OAAO,SAASC,UAAUA,CAACjB,GAAG,EAAE;EAC5B,IAAI,OAAOA,GAAG,KAAK,QAAQ,EAAE;IACzB,OAAOkB,UAAU,CAAClB,GAAG,CAAC;EAC1B;EACA;EACA,OAAOmB,IAAI,CAACC,IAAI,CAAC,CAACpB,GAAG,CAACiB,UAAU,IAAIjB,GAAG,CAACqB,IAAI,IAAIL,eAAe,CAAC;AACpE;AACA,SAASE,UAAUA,CAACI,GAAG,EAAE;EACrB,IAAIC,CAAC,GAAG,CAAC;IAAEC,MAAM,GAAG,CAAC;EACrB,KAAK,IAAIC,CAAC,GAAG,CAAC,EAAEC,CAAC,GAAGJ,GAAG,CAACE,MAAM,EAAEC,CAAC,GAAGC,CAAC,EAAED,CAAC,EAAE,EAAE;IACxCF,CAAC,GAAGD,GAAG,CAACK,UAAU,CAACF,CAAC,CAAC;IACrB,IAAIF,CAAC,GAAG,IAAI,EAAE;MACVC,MAAM,IAAI,CAAC;IACf,CAAC,MACI,IAAID,CAAC,GAAG,KAAK,EAAE;MAChBC,MAAM,IAAI,CAAC;IACf,CAAC,MACI,IAAID,CAAC,GAAG,MAAM,IAAIA,CAAC,IAAI,MAAM,EAAE;MAChCC,MAAM,IAAI,CAAC;IACf,CAAC,MACI;MACDC,CAAC,EAAE;MACHD,MAAM,IAAI,CAAC;IACf;EACJ;EACA,OAAOA,MAAM;AACjB;AACA;AACA;AACA;AACA,OAAO,SAASI,YAAYA,CAAA,EAAG;EAC3B,OAAQC,IAAI,CAACC,GAAG,CAAC,CAAC,CAACC,QAAQ,CAAC,EAAE,CAAC,CAACC,SAAS,CAAC,CAAC,CAAC,GACxCb,IAAI,CAACc,MAAM,CAAC,CAAC,CAACF,QAAQ,CAAC,EAAE,CAAC,CAACC,SAAS,CAAC,CAAC,EAAE,CAAC,CAAC;AAClD", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}