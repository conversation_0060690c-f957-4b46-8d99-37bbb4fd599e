{"ast": null, "code": "import { isBinary } from \"./is-binary.js\";\n/**\n * Replaces every Buffer | ArrayBuffer | Blob | File in packet with a numbered placeholder.\n *\n * @param {Object} packet - socket.io event packet\n * @return {Object} with deconstructed packet and list of buffers\n * @public\n */\nexport function deconstructPacket(packet) {\n  const buffers = [];\n  const packetData = packet.data;\n  const pack = packet;\n  pack.data = _deconstructPacket(packetData, buffers);\n  pack.attachments = buffers.length; // number of binary 'attachments'\n  return {\n    packet: pack,\n    buffers: buffers\n  };\n}\nfunction _deconstructPacket(data, buffers) {\n  if (!data) return data;\n  if (isBinary(data)) {\n    const placeholder = {\n      _placeholder: true,\n      num: buffers.length\n    };\n    buffers.push(data);\n    return placeholder;\n  } else if (Array.isArray(data)) {\n    const newData = new Array(data.length);\n    for (let i = 0; i < data.length; i++) {\n      newData[i] = _deconstructPacket(data[i], buffers);\n    }\n    return newData;\n  } else if (typeof data === \"object\" && !(data instanceof Date)) {\n    const newData = {};\n    for (const key in data) {\n      if (Object.prototype.hasOwnProperty.call(data, key)) {\n        newData[key] = _deconstructPacket(data[key], buffers);\n      }\n    }\n    return newData;\n  }\n  return data;\n}\n/**\n * Reconstructs a binary packet from its placeholder packet and buffers\n *\n * @param {Object} packet - event packet with placeholders\n * @param {Array} buffers - binary buffers to put in placeholder positions\n * @return {Object} reconstructed packet\n * @public\n */\nexport function reconstructPacket(packet, buffers) {\n  packet.data = _reconstructPacket(packet.data, buffers);\n  delete packet.attachments; // no longer useful\n  return packet;\n}\nfunction _reconstructPacket(data, buffers) {\n  if (!data) return data;\n  if (data && data._placeholder === true) {\n    const isIndexValid = typeof data.num === \"number\" && data.num >= 0 && data.num < buffers.length;\n    if (isIndexValid) {\n      return buffers[data.num]; // appropriate buffer (should be natural order anyway)\n    } else {\n      throw new Error(\"illegal attachments\");\n    }\n  } else if (Array.isArray(data)) {\n    for (let i = 0; i < data.length; i++) {\n      data[i] = _reconstructPacket(data[i], buffers);\n    }\n  } else if (typeof data === \"object\") {\n    for (const key in data) {\n      if (Object.prototype.hasOwnProperty.call(data, key)) {\n        data[key] = _reconstructPacket(data[key], buffers);\n      }\n    }\n  }\n  return data;\n}", "map": {"version": 3, "names": ["isBinary", "deconstructPacket", "packet", "buffers", "packetData", "data", "pack", "_deconstructPacket", "attachments", "length", "placeholder", "_placeholder", "num", "push", "Array", "isArray", "newData", "i", "Date", "key", "Object", "prototype", "hasOwnProperty", "call", "reconstructPacket", "_reconstructPacket", "isIndexValid", "Error"], "sources": ["C:/restraunat managment system/frontend/node_modules/socket.io-parser/build/esm/binary.js"], "sourcesContent": ["import { isBinary } from \"./is-binary.js\";\n/**\n * Replaces every Buffer | ArrayBuffer | Blob | File in packet with a numbered placeholder.\n *\n * @param {Object} packet - socket.io event packet\n * @return {Object} with deconstructed packet and list of buffers\n * @public\n */\nexport function deconstructPacket(packet) {\n    const buffers = [];\n    const packetData = packet.data;\n    const pack = packet;\n    pack.data = _deconstructPacket(packetData, buffers);\n    pack.attachments = buffers.length; // number of binary 'attachments'\n    return { packet: pack, buffers: buffers };\n}\nfunction _deconstructPacket(data, buffers) {\n    if (!data)\n        return data;\n    if (isBinary(data)) {\n        const placeholder = { _placeholder: true, num: buffers.length };\n        buffers.push(data);\n        return placeholder;\n    }\n    else if (Array.isArray(data)) {\n        const newData = new Array(data.length);\n        for (let i = 0; i < data.length; i++) {\n            newData[i] = _deconstructPacket(data[i], buffers);\n        }\n        return newData;\n    }\n    else if (typeof data === \"object\" && !(data instanceof Date)) {\n        const newData = {};\n        for (const key in data) {\n            if (Object.prototype.hasOwnProperty.call(data, key)) {\n                newData[key] = _deconstructPacket(data[key], buffers);\n            }\n        }\n        return newData;\n    }\n    return data;\n}\n/**\n * Reconstructs a binary packet from its placeholder packet and buffers\n *\n * @param {Object} packet - event packet with placeholders\n * @param {Array} buffers - binary buffers to put in placeholder positions\n * @return {Object} reconstructed packet\n * @public\n */\nexport function reconstructPacket(packet, buffers) {\n    packet.data = _reconstructPacket(packet.data, buffers);\n    delete packet.attachments; // no longer useful\n    return packet;\n}\nfunction _reconstructPacket(data, buffers) {\n    if (!data)\n        return data;\n    if (data && data._placeholder === true) {\n        const isIndexValid = typeof data.num === \"number\" &&\n            data.num >= 0 &&\n            data.num < buffers.length;\n        if (isIndexValid) {\n            return buffers[data.num]; // appropriate buffer (should be natural order anyway)\n        }\n        else {\n            throw new Error(\"illegal attachments\");\n        }\n    }\n    else if (Array.isArray(data)) {\n        for (let i = 0; i < data.length; i++) {\n            data[i] = _reconstructPacket(data[i], buffers);\n        }\n    }\n    else if (typeof data === \"object\") {\n        for (const key in data) {\n            if (Object.prototype.hasOwnProperty.call(data, key)) {\n                data[key] = _reconstructPacket(data[key], buffers);\n            }\n        }\n    }\n    return data;\n}\n"], "mappings": "AAAA,SAASA,QAAQ,QAAQ,gBAAgB;AACzC;AACA;AACA;AACA;AACA;AACA;AACA;AACA,OAAO,SAASC,iBAAiBA,CAACC,MAAM,EAAE;EACtC,MAAMC,OAAO,GAAG,EAAE;EAClB,MAAMC,UAAU,GAAGF,MAAM,CAACG,IAAI;EAC9B,MAAMC,IAAI,GAAGJ,MAAM;EACnBI,IAAI,CAACD,IAAI,GAAGE,kBAAkB,CAACH,UAAU,EAAED,OAAO,CAAC;EACnDG,IAAI,CAACE,WAAW,GAAGL,OAAO,CAACM,MAAM,CAAC,CAAC;EACnC,OAAO;IAAEP,MAAM,EAAEI,IAAI;IAAEH,OAAO,EAAEA;EAAQ,CAAC;AAC7C;AACA,SAASI,kBAAkBA,CAACF,IAAI,EAAEF,OAAO,EAAE;EACvC,IAAI,CAACE,IAAI,EACL,OAAOA,IAAI;EACf,IAAIL,QAAQ,CAACK,IAAI,CAAC,EAAE;IAChB,MAAMK,WAAW,GAAG;MAAEC,YAAY,EAAE,IAAI;MAAEC,GAAG,EAAET,OAAO,CAACM;IAAO,CAAC;IAC/DN,OAAO,CAACU,IAAI,CAACR,IAAI,CAAC;IAClB,OAAOK,WAAW;EACtB,CAAC,MACI,IAAII,KAAK,CAACC,OAAO,CAACV,IAAI,CAAC,EAAE;IAC1B,MAAMW,OAAO,GAAG,IAAIF,KAAK,CAACT,IAAI,CAACI,MAAM,CAAC;IACtC,KAAK,IAAIQ,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGZ,IAAI,CAACI,MAAM,EAAEQ,CAAC,EAAE,EAAE;MAClCD,OAAO,CAACC,CAAC,CAAC,GAAGV,kBAAkB,CAACF,IAAI,CAACY,CAAC,CAAC,EAAEd,OAAO,CAAC;IACrD;IACA,OAAOa,OAAO;EAClB,CAAC,MACI,IAAI,OAAOX,IAAI,KAAK,QAAQ,IAAI,EAAEA,IAAI,YAAYa,IAAI,CAAC,EAAE;IAC1D,MAAMF,OAAO,GAAG,CAAC,CAAC;IAClB,KAAK,MAAMG,GAAG,IAAId,IAAI,EAAE;MACpB,IAAIe,MAAM,CAACC,SAAS,CAACC,cAAc,CAACC,IAAI,CAAClB,IAAI,EAAEc,GAAG,CAAC,EAAE;QACjDH,OAAO,CAACG,GAAG,CAAC,GAAGZ,kBAAkB,CAACF,IAAI,CAACc,GAAG,CAAC,EAAEhB,OAAO,CAAC;MACzD;IACJ;IACA,OAAOa,OAAO;EAClB;EACA,OAAOX,IAAI;AACf;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,OAAO,SAASmB,iBAAiBA,CAACtB,MAAM,EAAEC,OAAO,EAAE;EAC/CD,MAAM,CAACG,IAAI,GAAGoB,kBAAkB,CAACvB,MAAM,CAACG,IAAI,EAAEF,OAAO,CAAC;EACtD,OAAOD,MAAM,CAACM,WAAW,CAAC,CAAC;EAC3B,OAAON,MAAM;AACjB;AACA,SAASuB,kBAAkBA,CAACpB,IAAI,EAAEF,OAAO,EAAE;EACvC,IAAI,CAACE,IAAI,EACL,OAAOA,IAAI;EACf,IAAIA,IAAI,IAAIA,IAAI,CAACM,YAAY,KAAK,IAAI,EAAE;IACpC,MAAMe,YAAY,GAAG,OAAOrB,IAAI,CAACO,GAAG,KAAK,QAAQ,IAC7CP,IAAI,CAACO,GAAG,IAAI,CAAC,IACbP,IAAI,CAACO,GAAG,GAAGT,OAAO,CAACM,MAAM;IAC7B,IAAIiB,YAAY,EAAE;MACd,OAAOvB,OAAO,CAACE,IAAI,CAACO,GAAG,CAAC,CAAC,CAAC;IAC9B,CAAC,MACI;MACD,MAAM,IAAIe,KAAK,CAAC,qBAAqB,CAAC;IAC1C;EACJ,CAAC,MACI,IAAIb,KAAK,CAACC,OAAO,CAACV,IAAI,CAAC,EAAE;IAC1B,KAAK,IAAIY,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGZ,IAAI,CAACI,MAAM,EAAEQ,CAAC,EAAE,EAAE;MAClCZ,IAAI,CAACY,CAAC,CAAC,GAAGQ,kBAAkB,CAACpB,IAAI,CAACY,CAAC,CAAC,EAAEd,OAAO,CAAC;IAClD;EACJ,CAAC,MACI,IAAI,OAAOE,IAAI,KAAK,QAAQ,EAAE;IAC/B,KAAK,MAAMc,GAAG,IAAId,IAAI,EAAE;MACpB,IAAIe,MAAM,CAACC,SAAS,CAACC,cAAc,CAACC,IAAI,CAAClB,IAAI,EAAEc,GAAG,CAAC,EAAE;QACjDd,IAAI,CAACc,GAAG,CAAC,GAAGM,kBAAkB,CAACpB,IAAI,CAACc,GAAG,CAAC,EAAEhB,OAAO,CAAC;MACtD;IACJ;EACJ;EACA,OAAOE,IAAI;AACf", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}