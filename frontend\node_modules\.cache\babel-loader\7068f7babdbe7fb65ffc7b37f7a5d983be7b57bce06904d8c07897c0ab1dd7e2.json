{"ast": null, "code": "var _jsxFileName = \"C:\\\\restraunat managment system\\\\frontend\\\\src\\\\components\\\\CustomerMenu.js\",\n  _s = $RefreshSig$();\nimport React, { useState, useEffect } from 'react';\nimport { useParams } from 'react-router-dom';\nimport axios from 'axios';\nimport { jsxDEV as _jsxDEV, Fragment as _Fragment } from \"react/jsx-dev-runtime\";\nconst API_BASE_URL = process.env.REACT_APP_API_BASE_URL;\nconst CustomerMenu = () => {\n  _s();\n  const {\n    tableId\n  } = useParams();\n  const [menu, setMenu] = useState({});\n  const [cart, setCart] = useState([]);\n  const [loading, setLoading] = useState(true);\n  const [error, setError] = useState('');\n  const [currentStep, setCurrentStep] = useState('menu'); // 'menu', 'checkout', 'payment', 'processing'\n  const [customerDetails, setCustomerDetails] = useState({\n    name: '',\n    phone: ''\n  });\n  const [orderData, setOrderData] = useState(null);\n  const [paymentProcessing, setPaymentProcessing] = useState(false);\n  useEffect(() => {\n    fetchMenu();\n    validateTable();\n  }, [tableId]);\n  const validateTable = async () => {\n    try {\n      await axios.get(`${API_BASE_URL}/api/tables/${tableId}`);\n    } catch (error) {\n      setError('Invalid table. Please scan a valid QR code.');\n    }\n  };\n  const fetchMenu = async () => {\n    try {\n      const response = await axios.get(`${API_BASE_URL}/api/menu`);\n      setMenu(response.data);\n      setLoading(false);\n    } catch (error) {\n      console.error('Error fetching menu:', error);\n      setError('Unable to load menu. Please try again.');\n      setLoading(false);\n    }\n  };\n  const addToCart = item => {\n    const existingItem = cart.find(cartItem => cartItem._id === item._id);\n    if (existingItem) {\n      setCart(cart.map(cartItem => cartItem._id === item._id ? {\n        ...cartItem,\n        quantity: cartItem.quantity + 1\n      } : cartItem));\n    } else {\n      setCart([...cart, {\n        ...item,\n        quantity: 1\n      }]);\n    }\n  };\n  const removeFromCart = itemId => {\n    setCart(cart.filter(item => item._id !== itemId));\n  };\n  const updateQuantity = (itemId, quantity) => {\n    if (quantity === 0) {\n      removeFromCart(itemId);\n    } else {\n      setCart(cart.map(item => item._id === itemId ? {\n        ...item,\n        quantity\n      } : item));\n    }\n  };\n  const getTotalAmount = () => {\n    return cart.reduce((total, item) => total + item.price * item.quantity, 0);\n  };\n  const proceedToCheckout = () => {\n    if (cart.length === 0) {\n      alert('Please add items to your cart before proceeding.');\n      return;\n    }\n    setCurrentStep('checkout');\n  };\n  const proceedToPayment = () => {\n    if (!customerDetails.name.trim() || !customerDetails.phone.trim()) {\n      alert('Please enter your name and phone number.');\n      return;\n    }\n    setCurrentStep('payment');\n  };\n  const processPayment = async paymentMethod => {\n    setPaymentProcessing(true);\n    try {\n      var _document$getElementB;\n      // Create order with payment method\n      const newOrderData = {\n        tableId,\n        items: cart.map(item => ({\n          menuItem: item._id,\n          quantity: item.quantity,\n          price: item.price\n        })),\n        customerName: customerDetails.name,\n        customerPhone: customerDetails.phone,\n        specialInstructions: ((_document$getElementB = document.getElementById('specialInstructions')) === null || _document$getElementB === void 0 ? void 0 : _document$getElementB.value) || '',\n        paymentMethod\n      };\n      const response = await axios.post(`${API_BASE_URL}/api/orders`, newOrderData);\n      setOrderData(response.data);\n      if (paymentMethod === 'online') {\n        // Process online payment\n        const paymentResponse = await axios.post(`${API_BASE_URL}/api/orders/${response.data._id}/payment/online`);\n        if (paymentResponse.data.success) {\n          alert(`Payment successful! Order placed successfully. Order number: ${response.data.orderNumber}`);\n          resetOrder();\n        } else {\n          alert('Payment failed. Please try again.');\n          setCurrentStep('menu');\n        }\n      } else if (paymentMethod === 'cash') {\n        alert(`Order created! Please wait for manager confirmation. Order number: ${response.data.orderNumber}`);\n        setCurrentStep('processing');\n      }\n    } catch (error) {\n      console.error('Error processing payment:', error);\n      alert('Error processing payment. Please try again.');\n      setCurrentStep('menu');\n    } finally {\n      setPaymentProcessing(false);\n    }\n  };\n  const resetOrder = () => {\n    setCart([]);\n    setCustomerDetails({\n      name: '',\n      phone: ''\n    });\n    setCurrentStep('menu');\n    setOrderData(null);\n  };\n  if (loading) return /*#__PURE__*/_jsxDEV(\"div\", {\n    className: \"customer-menu\",\n    children: /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"loading\",\n      children: [/*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"spinner\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 147,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n        children: \"Loading delicious menu...\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 148,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 146,\n      columnNumber: 7\n    }, this)\n  }, void 0, false, {\n    fileName: _jsxFileName,\n    lineNumber: 145,\n    columnNumber: 5\n  }, this);\n  if (error) return /*#__PURE__*/_jsxDEV(\"div\", {\n    className: \"customer-menu\",\n    children: /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"card\",\n      style: {\n        textAlign: 'center',\n        color: '#e74c3c'\n      },\n      children: [/*#__PURE__*/_jsxDEV(\"h2\", {\n        children: [\"\\u26A0\\uFE0F \", error]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 156,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n        className: \"btn btn-primary\",\n        onClick: () => window.location.reload(),\n        children: \"Try Again\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 157,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 155,\n      columnNumber: 7\n    }, this)\n  }, void 0, false, {\n    fileName: _jsxFileName,\n    lineNumber: 154,\n    columnNumber: 5\n  }, this);\n\n  // Render checkout step\n  if (currentStep === 'checkout') {\n    return /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"customer-menu\",\n      children: [/*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"menu-header\",\n        children: [/*#__PURE__*/_jsxDEV(\"h1\", {\n          children: \"\\uD83D\\uDCCB Order Summary\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 169,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"table-info\",\n          children: [\"\\uD83D\\uDCCD Table \", tableId]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 170,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 168,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"order-summary\",\n        children: [/*#__PURE__*/_jsxDEV(\"h3\", {\n          children: \"Your Order\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 174,\n          columnNumber: 11\n        }, this), cart.map(item => /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"cart-item\",\n          children: [/*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"cart-item-info\",\n            children: [/*#__PURE__*/_jsxDEV(\"h4\", {\n              children: item.name\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 178,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"cart-item-price\",\n              children: [\"$\", item.price.toFixed(2), \" \\xD7 \", item.quantity]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 179,\n              columnNumber: 17\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 177,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"item-total\",\n            children: [\"$\", (item.price * item.quantity).toFixed(2)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 181,\n            columnNumber: 15\n          }, this)]\n        }, item._id, true, {\n          fileName: _jsxFileName,\n          lineNumber: 176,\n          columnNumber: 13\n        }, this)), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"order-total\",\n          children: /*#__PURE__*/_jsxDEV(\"strong\", {\n            children: [\"Total: $\", getTotalAmount().toFixed(2)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 185,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 184,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 173,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"customer-details\",\n        children: [/*#__PURE__*/_jsxDEV(\"h3\", {\n          children: \"Customer Details\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 190,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"form-group\",\n          children: [/*#__PURE__*/_jsxDEV(\"label\", {\n            children: \"Name *\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 192,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"input\", {\n            type: \"text\",\n            value: customerDetails.name,\n            onChange: e => setCustomerDetails({\n              ...customerDetails,\n              name: e.target.value\n            }),\n            placeholder: \"Enter your name\",\n            required: true\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 193,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 191,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"form-group\",\n          children: [/*#__PURE__*/_jsxDEV(\"label\", {\n            children: \"Phone Number *\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 202,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"input\", {\n            type: \"tel\",\n            value: customerDetails.phone,\n            onChange: e => setCustomerDetails({\n              ...customerDetails,\n              phone: e.target.value\n            }),\n            placeholder: \"Enter your phone number\",\n            required: true\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 203,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 201,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"form-group\",\n          children: [/*#__PURE__*/_jsxDEV(\"label\", {\n            style: {\n              color: '#ffc107',\n              fontWeight: 'bold'\n            },\n            children: \"\\uD83D\\uDCDD Special Instructions (Optional)\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 212,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"textarea\", {\n            id: \"specialInstructions\",\n            placeholder: \"Any special requests, dietary requirements, or cooking preferences... (e.g., 'Extra spicy', 'No onions', 'Well done')\",\n            rows: \"4\",\n            style: {\n              border: '2px solid #ffc107',\n              borderRadius: '8px',\n              padding: '12px',\n              fontSize: '1rem'\n            }\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 215,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"small\", {\n            style: {\n              color: '#6c757d',\n              fontSize: '0.9rem'\n            },\n            children: \"\\uD83D\\uDCA1 Help our kitchen prepare your order exactly how you like it!\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 226,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 211,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 189,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"checkout-actions\",\n        children: [/*#__PURE__*/_jsxDEV(\"button\", {\n          className: \"btn btn-secondary\",\n          onClick: () => setCurrentStep('menu'),\n          children: \"\\u2190 Back to Menu\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 233,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n          className: \"btn btn-primary\",\n          onClick: proceedToPayment,\n          children: \"Proceed to Payment \\u2192\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 236,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 232,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 167,\n      columnNumber: 7\n    }, this);\n  }\n\n  // Render payment step\n  if (currentStep === 'payment') {\n    return /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"customer-menu\",\n      children: [/*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"menu-header\",\n        children: [/*#__PURE__*/_jsxDEV(\"h1\", {\n          children: \"\\uD83D\\uDCB3 Payment\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 249,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"table-info\",\n          children: [\"\\uD83D\\uDCCD Table \", tableId]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 250,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 248,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"payment-summary\",\n        children: [/*#__PURE__*/_jsxDEV(\"h3\", {\n          children: \"Order Summary\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 254,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"customer-info\",\n          children: [/*#__PURE__*/_jsxDEV(\"p\", {\n            children: [/*#__PURE__*/_jsxDEV(\"strong\", {\n              children: \"Name:\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 256,\n              columnNumber: 16\n            }, this), \" \", customerDetails.name]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 256,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n            children: [/*#__PURE__*/_jsxDEV(\"strong\", {\n              children: \"Phone:\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 257,\n              columnNumber: 16\n            }, this), \" \", customerDetails.phone]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 257,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 255,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"order-total\",\n          children: /*#__PURE__*/_jsxDEV(\"strong\", {\n            children: [\"Total Amount: $\", getTotalAmount().toFixed(2)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 260,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 259,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 253,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"payment-options\",\n        children: [/*#__PURE__*/_jsxDEV(\"h3\", {\n          children: \"Choose Payment Method\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 265,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"payment-buttons\",\n          children: [/*#__PURE__*/_jsxDEV(\"button\", {\n            className: \"payment-btn online-payment\",\n            onClick: () => processPayment('online'),\n            disabled: paymentProcessing,\n            children: paymentProcessing ? /*#__PURE__*/_jsxDEV(_Fragment, {\n              children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"spinner\",\n                style: {\n                  width: '20px',\n                  height: '20px',\n                  marginRight: '10px'\n                }\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 274,\n                columnNumber: 19\n              }, this), \"Processing...\"]\n            }, void 0, true) : /*#__PURE__*/_jsxDEV(_Fragment, {\n              children: \"\\uD83D\\uDCB3 Pay Online\"\n            }, void 0, false)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 267,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n            className: \"payment-btn cash-payment\",\n            onClick: () => processPayment('cash'),\n            disabled: paymentProcessing,\n            children: \"\\uD83D\\uDCB0 Pay with Cash\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 281,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 266,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 264,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"payment-actions\",\n        children: /*#__PURE__*/_jsxDEV(\"button\", {\n          className: \"btn btn-secondary\",\n          onClick: () => setCurrentStep('checkout'),\n          children: \"\\u2190 Back to Checkout\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 292,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 291,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 247,\n      columnNumber: 7\n    }, this);\n  }\n\n  // Render processing step (for cash payments)\n  if (currentStep === 'processing') {\n    return /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"customer-menu\",\n      children: [/*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"menu-header\",\n        children: [/*#__PURE__*/_jsxDEV(\"h1\", {\n          children: \"\\u23F3 Processing Order\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 305,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"table-info\",\n          children: [\"\\uD83D\\uDCCD Table \", tableId]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 306,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 304,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"processing-info\",\n        children: [/*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"processing-icon\",\n          children: \"\\u23F3\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 310,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"h3\", {\n          children: \"Waiting for Manager Confirmation\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 311,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n          children: \"Your cash payment order has been submitted and is waiting for manager confirmation.\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 312,\n          columnNumber: 11\n        }, this), orderData && /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"order-details\",\n          children: [/*#__PURE__*/_jsxDEV(\"p\", {\n            children: [/*#__PURE__*/_jsxDEV(\"strong\", {\n              children: \"Order Number:\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 315,\n              columnNumber: 18\n            }, this), \" \", orderData.orderNumber]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 315,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n            children: [/*#__PURE__*/_jsxDEV(\"strong\", {\n              children: \"Total Amount:\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 316,\n              columnNumber: 18\n            }, this), \" $\", orderData.totalAmount.toFixed(2)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 316,\n            columnNumber: 15\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 314,\n          columnNumber: 13\n        }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n          className: \"btn btn-primary\",\n          onClick: resetOrder,\n          children: \"Place New Order\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 319,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 309,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 303,\n      columnNumber: 7\n    }, this);\n  }\n  return /*#__PURE__*/_jsxDEV(\"div\", {\n    className: \"customer-menu\",\n    children: [/*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"menu-header\",\n      children: [/*#__PURE__*/_jsxDEV(\"h1\", {\n        children: \"\\uD83C\\uDF7D\\uFE0F Restaurant Menu\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 330,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"table-info\",\n        children: [\"\\uD83D\\uDCCD Table \", tableId]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 331,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 329,\n      columnNumber: 7\n    }, this), Object.keys(menu).length === 0 ? /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"card\",\n      style: {\n        textAlign: 'center'\n      },\n      children: [/*#__PURE__*/_jsxDEV(\"h2\", {\n        children: \"\\uD83C\\uDF7D\\uFE0F No menu items available\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 338,\n        columnNumber: 11\n      }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n        children: \"Please check back later or contact staff for assistance.\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 339,\n        columnNumber: 11\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 337,\n      columnNumber: 9\n    }, this) : Object.keys(menu).map(category => /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"menu-category\",\n      children: [/*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"category-header\",\n        children: /*#__PURE__*/_jsxDEV(\"h2\", {\n          children: [category === 'appetizers' && '🥗', category === 'mains' && '🍽️', category === 'desserts' && '🍰', category === 'beverages' && '🥤', category.charAt(0).toUpperCase() + category.slice(1)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 345,\n          columnNumber: 15\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 344,\n        columnNumber: 13\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"menu-items\",\n        children: menu[category].map(item => /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"menu-item\",\n          children: [/*#__PURE__*/_jsxDEV(\"h3\", {\n            children: item.name\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 356,\n            columnNumber: 19\n          }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n            children: item.description\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 357,\n            columnNumber: 19\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"price\",\n            children: [\"$\", item.price.toFixed(2)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 358,\n            columnNumber: 19\n          }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n            className: \"add-to-cart-btn\",\n            onClick: () => addToCart(item),\n            children: \"\\u2795 Add to Cart\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 359,\n            columnNumber: 19\n          }, this)]\n        }, item._id, true, {\n          fileName: _jsxFileName,\n          lineNumber: 355,\n          columnNumber: 17\n        }, this))\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 353,\n        columnNumber: 13\n      }, this)]\n    }, category, true, {\n      fileName: _jsxFileName,\n      lineNumber: 343,\n      columnNumber: 11\n    }, this)), cart.length > 0 && /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"cart\",\n      children: [/*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"cart-header\",\n        children: [/*#__PURE__*/_jsxDEV(\"h3\", {\n          children: \"\\uD83D\\uDED2 Your Order\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 375,\n          columnNumber: 13\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"cart-total\",\n          children: [\"$\", getTotalAmount().toFixed(2)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 376,\n          columnNumber: 13\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 374,\n        columnNumber: 11\n      }, this), cart.map(item => /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"cart-item\",\n        children: [/*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"cart-item-info\",\n          children: [/*#__PURE__*/_jsxDEV(\"h4\", {\n            children: item.name\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 384,\n            columnNumber: 17\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"cart-item-price\",\n            children: [\"$\", item.price.toFixed(2), \" each\"]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 385,\n            columnNumber: 17\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 383,\n          columnNumber: 15\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"quantity-controls\",\n          children: [/*#__PURE__*/_jsxDEV(\"button\", {\n            className: \"quantity-btn\",\n            onClick: () => updateQuantity(item._id, item.quantity - 1),\n            children: \"\\u2212\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 388,\n            columnNumber: 17\n          }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n            className: \"quantity\",\n            children: item.quantity\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 394,\n            columnNumber: 17\n          }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n            className: \"quantity-btn\",\n            onClick: () => updateQuantity(item._id, item.quantity + 1),\n            children: \"+\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 395,\n            columnNumber: 17\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 387,\n          columnNumber: 15\n        }, this)]\n      }, item._id, true, {\n        fileName: _jsxFileName,\n        lineNumber: 382,\n        columnNumber: 13\n      }, this)), /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"cart-actions\",\n        children: /*#__PURE__*/_jsxDEV(\"button\", {\n          className: \"checkout-btn\",\n          onClick: proceedToCheckout,\n          children: [\"\\uD83D\\uDED2 Proceed to Checkout - $\", getTotalAmount().toFixed(2)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 406,\n          columnNumber: 13\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 405,\n        columnNumber: 11\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 373,\n      columnNumber: 9\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 328,\n    columnNumber: 5\n  }, this);\n};\n_s(CustomerMenu, \"a6mu6Grx/9mwOm3GAYJnhehT/hM=\", false, function () {\n  return [useParams];\n});\n_c = CustomerMenu;\nexport default CustomerMenu;\nvar _c;\n$RefreshReg$(_c, \"CustomerMenu\");", "map": {"version": 3, "names": ["React", "useState", "useEffect", "useParams", "axios", "jsxDEV", "_jsxDEV", "Fragment", "_Fragment", "API_BASE_URL", "process", "env", "REACT_APP_API_BASE_URL", "CustomerMenu", "_s", "tableId", "menu", "setMenu", "cart", "setCart", "loading", "setLoading", "error", "setError", "currentStep", "setCurrentStep", "customerDetails", "setCustomerDetails", "name", "phone", "orderData", "setOrderData", "paymentProcessing", "setPaymentProcessing", "fetchMenu", "validateTable", "get", "response", "data", "console", "addToCart", "item", "existingItem", "find", "cartItem", "_id", "map", "quantity", "removeFromCart", "itemId", "filter", "updateQuantity", "getTotalAmount", "reduce", "total", "price", "proceedToCheckout", "length", "alert", "proceedToPayment", "trim", "processPayment", "paymentMethod", "_document$getElementB", "newOrderData", "items", "menuItem", "customerName", "customerPhone", "specialInstructions", "document", "getElementById", "value", "post", "paymentResponse", "success", "orderNumber", "resetOrder", "className", "children", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "style", "textAlign", "color", "onClick", "window", "location", "reload", "toFixed", "type", "onChange", "e", "target", "placeholder", "required", "fontWeight", "id", "rows", "border", "borderRadius", "padding", "fontSize", "disabled", "width", "height", "marginRight", "totalAmount", "Object", "keys", "category", "char<PERSON>t", "toUpperCase", "slice", "description", "_c", "$RefreshReg$"], "sources": ["C:/restraunat managment system/frontend/src/components/CustomerMenu.js"], "sourcesContent": ["import React, { useState, useEffect } from 'react';\nimport { useParams } from 'react-router-dom';\nimport axios from 'axios';\n\nconst API_BASE_URL = process.env.REACT_APP_API_BASE_URL;\n\nconst CustomerMenu = () => {\n  const { tableId } = useParams();\n  const [menu, setMenu] = useState({});\n  const [cart, setCart] = useState([]);\n  const [loading, setLoading] = useState(true);\n  const [error, setError] = useState('');\n  const [currentStep, setCurrentStep] = useState('menu'); // 'menu', 'checkout', 'payment', 'processing'\n  const [customerDetails, setCustomerDetails] = useState({\n    name: '',\n    phone: ''\n  });\n  const [orderData, setOrderData] = useState(null);\n  const [paymentProcessing, setPaymentProcessing] = useState(false);\n\n  useEffect(() => {\n    fetchMenu();\n    validateTable();\n  }, [tableId]);\n\n  const validateTable = async () => {\n    try {\n      await axios.get(`${API_BASE_URL}/api/tables/${tableId}`);\n    } catch (error) {\n      setError('Invalid table. Please scan a valid QR code.');\n    }\n  };\n\n  const fetchMenu = async () => {\n    try {\n      const response = await axios.get(`${API_BASE_URL}/api/menu`);\n      setMenu(response.data);\n      setLoading(false);\n    } catch (error) {\n      console.error('Error fetching menu:', error);\n      setError('Unable to load menu. Please try again.');\n      setLoading(false);\n    }\n  };\n\n  const addToCart = (item) => {\n    const existingItem = cart.find(cartItem => cartItem._id === item._id);\n    if (existingItem) {\n      setCart(cart.map(cartItem =>\n        cartItem._id === item._id\n          ? { ...cartItem, quantity: cartItem.quantity + 1 }\n          : cartItem\n      ));\n    } else {\n      setCart([...cart, { ...item, quantity: 1 }]);\n    }\n  };\n\n  const removeFromCart = (itemId) => {\n    setCart(cart.filter(item => item._id !== itemId));\n  };\n\n  const updateQuantity = (itemId, quantity) => {\n    if (quantity === 0) {\n      removeFromCart(itemId);\n    } else {\n      setCart(cart.map(item =>\n        item._id === itemId ? { ...item, quantity } : item\n      ));\n    }\n  };\n\n  const getTotalAmount = () => {\n    return cart.reduce((total, item) => total + (item.price * item.quantity), 0);\n  };\n\n  const proceedToCheckout = () => {\n    if (cart.length === 0) {\n      alert('Please add items to your cart before proceeding.');\n      return;\n    }\n    setCurrentStep('checkout');\n  };\n\n  const proceedToPayment = () => {\n    if (!customerDetails.name.trim() || !customerDetails.phone.trim()) {\n      alert('Please enter your name and phone number.');\n      return;\n    }\n    setCurrentStep('payment');\n  };\n\n  const processPayment = async (paymentMethod) => {\n    setPaymentProcessing(true);\n    try {\n      // Create order with payment method\n      const newOrderData = {\n        tableId,\n        items: cart.map(item => ({\n          menuItem: item._id,\n          quantity: item.quantity,\n          price: item.price\n        })),\n        customerName: customerDetails.name,\n        customerPhone: customerDetails.phone,\n        specialInstructions: document.getElementById('specialInstructions')?.value || '',\n        paymentMethod\n      };\n\n      const response = await axios.post(`${API_BASE_URL}/api/orders`, newOrderData);\n      setOrderData(response.data);\n\n      if (paymentMethod === 'online') {\n        // Process online payment\n        const paymentResponse = await axios.post(`${API_BASE_URL}/api/orders/${response.data._id}/payment/online`);\n\n        if (paymentResponse.data.success) {\n          alert(`Payment successful! Order placed successfully. Order number: ${response.data.orderNumber}`);\n          resetOrder();\n        } else {\n          alert('Payment failed. Please try again.');\n          setCurrentStep('menu');\n        }\n      } else if (paymentMethod === 'cash') {\n        alert(`Order created! Please wait for manager confirmation. Order number: ${response.data.orderNumber}`);\n        setCurrentStep('processing');\n      }\n    } catch (error) {\n      console.error('Error processing payment:', error);\n      alert('Error processing payment. Please try again.');\n      setCurrentStep('menu');\n    } finally {\n      setPaymentProcessing(false);\n    }\n  };\n\n  const resetOrder = () => {\n    setCart([]);\n    setCustomerDetails({ name: '', phone: '' });\n    setCurrentStep('menu');\n    setOrderData(null);\n  };\n\n  if (loading) return (\n    <div className=\"customer-menu\">\n      <div className=\"loading\">\n        <div className=\"spinner\"></div>\n        <p>Loading delicious menu...</p>\n      </div>\n    </div>\n  );\n\n  if (error) return (\n    <div className=\"customer-menu\">\n      <div className=\"card\" style={{textAlign: 'center', color: '#e74c3c'}}>\n        <h2>⚠️ {error}</h2>\n        <button className=\"btn btn-primary\" onClick={() => window.location.reload()}>\n          Try Again\n        </button>\n      </div>\n    </div>\n  );\n\n  // Render checkout step\n  if (currentStep === 'checkout') {\n    return (\n      <div className=\"customer-menu\">\n        <div className=\"menu-header\">\n          <h1>📋 Order Summary</h1>\n          <div className=\"table-info\">📍 Table {tableId}</div>\n        </div>\n\n        <div className=\"order-summary\">\n          <h3>Your Order</h3>\n          {cart.map(item => (\n            <div key={item._id} className=\"cart-item\">\n              <div className=\"cart-item-info\">\n                <h4>{item.name}</h4>\n                <div className=\"cart-item-price\">${item.price.toFixed(2)} × {item.quantity}</div>\n              </div>\n              <div className=\"item-total\">${(item.price * item.quantity).toFixed(2)}</div>\n            </div>\n          ))}\n          <div className=\"order-total\">\n            <strong>Total: ${getTotalAmount().toFixed(2)}</strong>\n          </div>\n        </div>\n\n        <div className=\"customer-details\">\n          <h3>Customer Details</h3>\n          <div className=\"form-group\">\n            <label>Name *</label>\n            <input\n              type=\"text\"\n              value={customerDetails.name}\n              onChange={(e) => setCustomerDetails({...customerDetails, name: e.target.value})}\n              placeholder=\"Enter your name\"\n              required\n            />\n          </div>\n          <div className=\"form-group\">\n            <label>Phone Number *</label>\n            <input\n              type=\"tel\"\n              value={customerDetails.phone}\n              onChange={(e) => setCustomerDetails({...customerDetails, phone: e.target.value})}\n              placeholder=\"Enter your phone number\"\n              required\n            />\n          </div>\n          <div className=\"form-group\">\n            <label style={{color: '#ffc107', fontWeight: 'bold'}}>\n              📝 Special Instructions (Optional)\n            </label>\n            <textarea\n              id=\"specialInstructions\"\n              placeholder=\"Any special requests, dietary requirements, or cooking preferences... (e.g., 'Extra spicy', 'No onions', 'Well done')\"\n              rows=\"4\"\n              style={{\n                border: '2px solid #ffc107',\n                borderRadius: '8px',\n                padding: '12px',\n                fontSize: '1rem'\n              }}\n            />\n            <small style={{color: '#6c757d', fontSize: '0.9rem'}}>\n              💡 Help our kitchen prepare your order exactly how you like it!\n            </small>\n          </div>\n        </div>\n\n        <div className=\"checkout-actions\">\n          <button className=\"btn btn-secondary\" onClick={() => setCurrentStep('menu')}>\n            ← Back to Menu\n          </button>\n          <button className=\"btn btn-primary\" onClick={proceedToPayment}>\n            Proceed to Payment →\n          </button>\n        </div>\n      </div>\n    );\n  }\n\n  // Render payment step\n  if (currentStep === 'payment') {\n    return (\n      <div className=\"customer-menu\">\n        <div className=\"menu-header\">\n          <h1>💳 Payment</h1>\n          <div className=\"table-info\">📍 Table {tableId}</div>\n        </div>\n\n        <div className=\"payment-summary\">\n          <h3>Order Summary</h3>\n          <div className=\"customer-info\">\n            <p><strong>Name:</strong> {customerDetails.name}</p>\n            <p><strong>Phone:</strong> {customerDetails.phone}</p>\n          </div>\n          <div className=\"order-total\">\n            <strong>Total Amount: ${getTotalAmount().toFixed(2)}</strong>\n          </div>\n        </div>\n\n        <div className=\"payment-options\">\n          <h3>Choose Payment Method</h3>\n          <div className=\"payment-buttons\">\n            <button\n              className=\"payment-btn online-payment\"\n              onClick={() => processPayment('online')}\n              disabled={paymentProcessing}\n            >\n              {paymentProcessing ? (\n                <>\n                  <div className=\"spinner\" style={{width: '20px', height: '20px', marginRight: '10px'}}></div>\n                  Processing...\n                </>\n              ) : (\n                <>💳 Pay Online</>\n              )}\n            </button>\n            <button\n              className=\"payment-btn cash-payment\"\n              onClick={() => processPayment('cash')}\n              disabled={paymentProcessing}\n            >\n              💰 Pay with Cash\n            </button>\n          </div>\n        </div>\n\n        <div className=\"payment-actions\">\n          <button className=\"btn btn-secondary\" onClick={() => setCurrentStep('checkout')}>\n            ← Back to Checkout\n          </button>\n        </div>\n      </div>\n    );\n  }\n\n  // Render processing step (for cash payments)\n  if (currentStep === 'processing') {\n    return (\n      <div className=\"customer-menu\">\n        <div className=\"menu-header\">\n          <h1>⏳ Processing Order</h1>\n          <div className=\"table-info\">📍 Table {tableId}</div>\n        </div>\n\n        <div className=\"processing-info\">\n          <div className=\"processing-icon\">⏳</div>\n          <h3>Waiting for Manager Confirmation</h3>\n          <p>Your cash payment order has been submitted and is waiting for manager confirmation.</p>\n          {orderData && (\n            <div className=\"order-details\">\n              <p><strong>Order Number:</strong> {orderData.orderNumber}</p>\n              <p><strong>Total Amount:</strong> ${orderData.totalAmount.toFixed(2)}</p>\n            </div>\n          )}\n          <button className=\"btn btn-primary\" onClick={resetOrder}>\n            Place New Order\n          </button>\n        </div>\n      </div>\n    );\n  }\n\n  return (\n    <div className=\"customer-menu\">\n      <div className=\"menu-header\">\n        <h1>🍽️ Restaurant Menu</h1>\n        <div className=\"table-info\">\n          📍 Table {tableId}\n        </div>\n      </div>\n\n      {Object.keys(menu).length === 0 ? (\n        <div className=\"card\" style={{textAlign: 'center'}}>\n          <h2>🍽️ No menu items available</h2>\n          <p>Please check back later or contact staff for assistance.</p>\n        </div>\n      ) : (\n        Object.keys(menu).map(category => (\n          <div key={category} className=\"menu-category\">\n            <div className=\"category-header\">\n              <h2>\n                {category === 'appetizers' && '🥗'}\n                {category === 'mains' && '🍽️'}\n                {category === 'desserts' && '🍰'}\n                {category === 'beverages' && '🥤'}\n                {category.charAt(0).toUpperCase() + category.slice(1)}\n              </h2>\n            </div>\n            <div className=\"menu-items\">\n              {menu[category].map(item => (\n                <div key={item._id} className=\"menu-item\">\n                  <h3>{item.name}</h3>\n                  <p>{item.description}</p>\n                  <div className=\"price\">${item.price.toFixed(2)}</div>\n                  <button\n                    className=\"add-to-cart-btn\"\n                    onClick={() => addToCart(item)}\n                  >\n                    ➕ Add to Cart\n                  </button>\n                </div>\n              ))}\n            </div>\n          </div>\n        ))\n      )}\n\n      {cart.length > 0 && (\n        <div className=\"cart\">\n          <div className=\"cart-header\">\n            <h3>🛒 Your Order</h3>\n            <div className=\"cart-total\">\n              ${getTotalAmount().toFixed(2)}\n            </div>\n          </div>\n\n          {cart.map(item => (\n            <div key={item._id} className=\"cart-item\">\n              <div className=\"cart-item-info\">\n                <h4>{item.name}</h4>\n                <div className=\"cart-item-price\">${item.price.toFixed(2)} each</div>\n              </div>\n              <div className=\"quantity-controls\">\n                <button\n                  className=\"quantity-btn\"\n                  onClick={() => updateQuantity(item._id, item.quantity - 1)}\n                >\n                  −\n                </button>\n                <span className=\"quantity\">{item.quantity}</span>\n                <button\n                  className=\"quantity-btn\"\n                  onClick={() => updateQuantity(item._id, item.quantity + 1)}\n                >\n                  +\n                </button>\n              </div>\n            </div>\n          ))}\n\n          <div className=\"cart-actions\">\n            <button\n              className=\"checkout-btn\"\n              onClick={proceedToCheckout}\n            >\n              🛒 Proceed to Checkout - ${getTotalAmount().toFixed(2)}\n            </button>\n          </div>\n        </div>\n      )}\n    </div>\n  );\n};\n\nexport default CustomerMenu;\n"], "mappings": ";;AAAA,OAAOA,KAAK,IAAIC,QAAQ,EAAEC,SAAS,QAAQ,OAAO;AAClD,SAASC,SAAS,QAAQ,kBAAkB;AAC5C,OAAOC,KAAK,MAAM,OAAO;AAAC,SAAAC,MAAA,IAAAC,OAAA,EAAAC,QAAA,IAAAC,SAAA;AAE1B,MAAMC,YAAY,GAAGC,OAAO,CAACC,GAAG,CAACC,sBAAsB;AAEvD,MAAMC,YAAY,GAAGA,CAAA,KAAM;EAAAC,EAAA;EACzB,MAAM;IAAEC;EAAQ,CAAC,GAAGZ,SAAS,CAAC,CAAC;EAC/B,MAAM,CAACa,IAAI,EAAEC,OAAO,CAAC,GAAGhB,QAAQ,CAAC,CAAC,CAAC,CAAC;EACpC,MAAM,CAACiB,IAAI,EAAEC,OAAO,CAAC,GAAGlB,QAAQ,CAAC,EAAE,CAAC;EACpC,MAAM,CAACmB,OAAO,EAAEC,UAAU,CAAC,GAAGpB,QAAQ,CAAC,IAAI,CAAC;EAC5C,MAAM,CAACqB,KAAK,EAAEC,QAAQ,CAAC,GAAGtB,QAAQ,CAAC,EAAE,CAAC;EACtC,MAAM,CAACuB,WAAW,EAAEC,cAAc,CAAC,GAAGxB,QAAQ,CAAC,MAAM,CAAC,CAAC,CAAC;EACxD,MAAM,CAACyB,eAAe,EAAEC,kBAAkB,CAAC,GAAG1B,QAAQ,CAAC;IACrD2B,IAAI,EAAE,EAAE;IACRC,KAAK,EAAE;EACT,CAAC,CAAC;EACF,MAAM,CAACC,SAAS,EAAEC,YAAY,CAAC,GAAG9B,QAAQ,CAAC,IAAI,CAAC;EAChD,MAAM,CAAC+B,iBAAiB,EAAEC,oBAAoB,CAAC,GAAGhC,QAAQ,CAAC,KAAK,CAAC;EAEjEC,SAAS,CAAC,MAAM;IACdgC,SAAS,CAAC,CAAC;IACXC,aAAa,CAAC,CAAC;EACjB,CAAC,EAAE,CAACpB,OAAO,CAAC,CAAC;EAEb,MAAMoB,aAAa,GAAG,MAAAA,CAAA,KAAY;IAChC,IAAI;MACF,MAAM/B,KAAK,CAACgC,GAAG,CAAC,GAAG3B,YAAY,eAAeM,OAAO,EAAE,CAAC;IAC1D,CAAC,CAAC,OAAOO,KAAK,EAAE;MACdC,QAAQ,CAAC,6CAA6C,CAAC;IACzD;EACF,CAAC;EAED,MAAMW,SAAS,GAAG,MAAAA,CAAA,KAAY;IAC5B,IAAI;MACF,MAAMG,QAAQ,GAAG,MAAMjC,KAAK,CAACgC,GAAG,CAAC,GAAG3B,YAAY,WAAW,CAAC;MAC5DQ,OAAO,CAACoB,QAAQ,CAACC,IAAI,CAAC;MACtBjB,UAAU,CAAC,KAAK,CAAC;IACnB,CAAC,CAAC,OAAOC,KAAK,EAAE;MACdiB,OAAO,CAACjB,KAAK,CAAC,sBAAsB,EAAEA,KAAK,CAAC;MAC5CC,QAAQ,CAAC,wCAAwC,CAAC;MAClDF,UAAU,CAAC,KAAK,CAAC;IACnB;EACF,CAAC;EAED,MAAMmB,SAAS,GAAIC,IAAI,IAAK;IAC1B,MAAMC,YAAY,GAAGxB,IAAI,CAACyB,IAAI,CAACC,QAAQ,IAAIA,QAAQ,CAACC,GAAG,KAAKJ,IAAI,CAACI,GAAG,CAAC;IACrE,IAAIH,YAAY,EAAE;MAChBvB,OAAO,CAACD,IAAI,CAAC4B,GAAG,CAACF,QAAQ,IACvBA,QAAQ,CAACC,GAAG,KAAKJ,IAAI,CAACI,GAAG,GACrB;QAAE,GAAGD,QAAQ;QAAEG,QAAQ,EAAEH,QAAQ,CAACG,QAAQ,GAAG;MAAE,CAAC,GAChDH,QACN,CAAC,CAAC;IACJ,CAAC,MAAM;MACLzB,OAAO,CAAC,CAAC,GAAGD,IAAI,EAAE;QAAE,GAAGuB,IAAI;QAAEM,QAAQ,EAAE;MAAE,CAAC,CAAC,CAAC;IAC9C;EACF,CAAC;EAED,MAAMC,cAAc,GAAIC,MAAM,IAAK;IACjC9B,OAAO,CAACD,IAAI,CAACgC,MAAM,CAACT,IAAI,IAAIA,IAAI,CAACI,GAAG,KAAKI,MAAM,CAAC,CAAC;EACnD,CAAC;EAED,MAAME,cAAc,GAAGA,CAACF,MAAM,EAAEF,QAAQ,KAAK;IAC3C,IAAIA,QAAQ,KAAK,CAAC,EAAE;MAClBC,cAAc,CAACC,MAAM,CAAC;IACxB,CAAC,MAAM;MACL9B,OAAO,CAACD,IAAI,CAAC4B,GAAG,CAACL,IAAI,IACnBA,IAAI,CAACI,GAAG,KAAKI,MAAM,GAAG;QAAE,GAAGR,IAAI;QAAEM;MAAS,CAAC,GAAGN,IAChD,CAAC,CAAC;IACJ;EACF,CAAC;EAED,MAAMW,cAAc,GAAGA,CAAA,KAAM;IAC3B,OAAOlC,IAAI,CAACmC,MAAM,CAAC,CAACC,KAAK,EAAEb,IAAI,KAAKa,KAAK,GAAIb,IAAI,CAACc,KAAK,GAAGd,IAAI,CAACM,QAAS,EAAE,CAAC,CAAC;EAC9E,CAAC;EAED,MAAMS,iBAAiB,GAAGA,CAAA,KAAM;IAC9B,IAAItC,IAAI,CAACuC,MAAM,KAAK,CAAC,EAAE;MACrBC,KAAK,CAAC,kDAAkD,CAAC;MACzD;IACF;IACAjC,cAAc,CAAC,UAAU,CAAC;EAC5B,CAAC;EAED,MAAMkC,gBAAgB,GAAGA,CAAA,KAAM;IAC7B,IAAI,CAACjC,eAAe,CAACE,IAAI,CAACgC,IAAI,CAAC,CAAC,IAAI,CAAClC,eAAe,CAACG,KAAK,CAAC+B,IAAI,CAAC,CAAC,EAAE;MACjEF,KAAK,CAAC,0CAA0C,CAAC;MACjD;IACF;IACAjC,cAAc,CAAC,SAAS,CAAC;EAC3B,CAAC;EAED,MAAMoC,cAAc,GAAG,MAAOC,aAAa,IAAK;IAC9C7B,oBAAoB,CAAC,IAAI,CAAC;IAC1B,IAAI;MAAA,IAAA8B,qBAAA;MACF;MACA,MAAMC,YAAY,GAAG;QACnBjD,OAAO;QACPkD,KAAK,EAAE/C,IAAI,CAAC4B,GAAG,CAACL,IAAI,KAAK;UACvByB,QAAQ,EAAEzB,IAAI,CAACI,GAAG;UAClBE,QAAQ,EAAEN,IAAI,CAACM,QAAQ;UACvBQ,KAAK,EAAEd,IAAI,CAACc;QACd,CAAC,CAAC,CAAC;QACHY,YAAY,EAAEzC,eAAe,CAACE,IAAI;QAClCwC,aAAa,EAAE1C,eAAe,CAACG,KAAK;QACpCwC,mBAAmB,EAAE,EAAAN,qBAAA,GAAAO,QAAQ,CAACC,cAAc,CAAC,qBAAqB,CAAC,cAAAR,qBAAA,uBAA9CA,qBAAA,CAAgDS,KAAK,KAAI,EAAE;QAChFV;MACF,CAAC;MAED,MAAMzB,QAAQ,GAAG,MAAMjC,KAAK,CAACqE,IAAI,CAAC,GAAGhE,YAAY,aAAa,EAAEuD,YAAY,CAAC;MAC7EjC,YAAY,CAACM,QAAQ,CAACC,IAAI,CAAC;MAE3B,IAAIwB,aAAa,KAAK,QAAQ,EAAE;QAC9B;QACA,MAAMY,eAAe,GAAG,MAAMtE,KAAK,CAACqE,IAAI,CAAC,GAAGhE,YAAY,eAAe4B,QAAQ,CAACC,IAAI,CAACO,GAAG,iBAAiB,CAAC;QAE1G,IAAI6B,eAAe,CAACpC,IAAI,CAACqC,OAAO,EAAE;UAChCjB,KAAK,CAAC,gEAAgErB,QAAQ,CAACC,IAAI,CAACsC,WAAW,EAAE,CAAC;UAClGC,UAAU,CAAC,CAAC;QACd,CAAC,MAAM;UACLnB,KAAK,CAAC,mCAAmC,CAAC;UAC1CjC,cAAc,CAAC,MAAM,CAAC;QACxB;MACF,CAAC,MAAM,IAAIqC,aAAa,KAAK,MAAM,EAAE;QACnCJ,KAAK,CAAC,sEAAsErB,QAAQ,CAACC,IAAI,CAACsC,WAAW,EAAE,CAAC;QACxGnD,cAAc,CAAC,YAAY,CAAC;MAC9B;IACF,CAAC,CAAC,OAAOH,KAAK,EAAE;MACdiB,OAAO,CAACjB,KAAK,CAAC,2BAA2B,EAAEA,KAAK,CAAC;MACjDoC,KAAK,CAAC,6CAA6C,CAAC;MACpDjC,cAAc,CAAC,MAAM,CAAC;IACxB,CAAC,SAAS;MACRQ,oBAAoB,CAAC,KAAK,CAAC;IAC7B;EACF,CAAC;EAED,MAAM4C,UAAU,GAAGA,CAAA,KAAM;IACvB1D,OAAO,CAAC,EAAE,CAAC;IACXQ,kBAAkB,CAAC;MAAEC,IAAI,EAAE,EAAE;MAAEC,KAAK,EAAE;IAAG,CAAC,CAAC;IAC3CJ,cAAc,CAAC,MAAM,CAAC;IACtBM,YAAY,CAAC,IAAI,CAAC;EACpB,CAAC;EAED,IAAIX,OAAO,EAAE,oBACXd,OAAA;IAAKwE,SAAS,EAAC,eAAe;IAAAC,QAAA,eAC5BzE,OAAA;MAAKwE,SAAS,EAAC,SAAS;MAAAC,QAAA,gBACtBzE,OAAA;QAAKwE,SAAS,EAAC;MAAS;QAAAE,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAM,CAAC,eAC/B7E,OAAA;QAAAyE,QAAA,EAAG;MAAyB;QAAAC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAG,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAC7B;EAAC;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACH,CAAC;EAGR,IAAI7D,KAAK,EAAE,oBACThB,OAAA;IAAKwE,SAAS,EAAC,eAAe;IAAAC,QAAA,eAC5BzE,OAAA;MAAKwE,SAAS,EAAC,MAAM;MAACM,KAAK,EAAE;QAACC,SAAS,EAAE,QAAQ;QAAEC,KAAK,EAAE;MAAS,CAAE;MAAAP,QAAA,gBACnEzE,OAAA;QAAAyE,QAAA,GAAI,eAAG,EAACzD,KAAK;MAAA;QAAA0D,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAK,CAAC,eACnB7E,OAAA;QAAQwE,SAAS,EAAC,iBAAiB;QAACS,OAAO,EAAEA,CAAA,KAAMC,MAAM,CAACC,QAAQ,CAACC,MAAM,CAAC,CAAE;QAAAX,QAAA,EAAC;MAE7E;QAAAC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAQ,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACN;EAAC;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACH,CAAC;;EAGR;EACA,IAAI3D,WAAW,KAAK,UAAU,EAAE;IAC9B,oBACElB,OAAA;MAAKwE,SAAS,EAAC,eAAe;MAAAC,QAAA,gBAC5BzE,OAAA;QAAKwE,SAAS,EAAC,aAAa;QAAAC,QAAA,gBAC1BzE,OAAA;UAAAyE,QAAA,EAAI;QAAgB;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAI,CAAC,eACzB7E,OAAA;UAAKwE,SAAS,EAAC,YAAY;UAAAC,QAAA,GAAC,qBAAS,EAAChE,OAAO;QAAA;UAAAiE,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAM,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACjD,CAAC,eAEN7E,OAAA;QAAKwE,SAAS,EAAC,eAAe;QAAAC,QAAA,gBAC5BzE,OAAA;UAAAyE,QAAA,EAAI;QAAU;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAI,CAAC,EAClBjE,IAAI,CAAC4B,GAAG,CAACL,IAAI,iBACZnC,OAAA;UAAoBwE,SAAS,EAAC,WAAW;UAAAC,QAAA,gBACvCzE,OAAA;YAAKwE,SAAS,EAAC,gBAAgB;YAAAC,QAAA,gBAC7BzE,OAAA;cAAAyE,QAAA,EAAKtC,IAAI,CAACb;YAAI;cAAAoD,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAK,CAAC,eACpB7E,OAAA;cAAKwE,SAAS,EAAC,iBAAiB;cAAAC,QAAA,GAAC,GAAC,EAACtC,IAAI,CAACc,KAAK,CAACoC,OAAO,CAAC,CAAC,CAAC,EAAC,QAAG,EAAClD,IAAI,CAACM,QAAQ;YAAA;cAAAiC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAM,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAC9E,CAAC,eACN7E,OAAA;YAAKwE,SAAS,EAAC,YAAY;YAAAC,QAAA,GAAC,GAAC,EAAC,CAACtC,IAAI,CAACc,KAAK,GAAGd,IAAI,CAACM,QAAQ,EAAE4C,OAAO,CAAC,CAAC,CAAC;UAAA;YAAAX,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAM,CAAC;QAAA,GALpE1C,IAAI,CAACI,GAAG;UAAAmC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAMb,CACN,CAAC,eACF7E,OAAA;UAAKwE,SAAS,EAAC,aAAa;UAAAC,QAAA,eAC1BzE,OAAA;YAAAyE,QAAA,GAAQ,UAAQ,EAAC3B,cAAc,CAAC,CAAC,CAACuC,OAAO,CAAC,CAAC,CAAC;UAAA;YAAAX,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAS;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACnD,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH,CAAC,eAEN7E,OAAA;QAAKwE,SAAS,EAAC,kBAAkB;QAAAC,QAAA,gBAC/BzE,OAAA;UAAAyE,QAAA,EAAI;QAAgB;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAI,CAAC,eACzB7E,OAAA;UAAKwE,SAAS,EAAC,YAAY;UAAAC,QAAA,gBACzBzE,OAAA;YAAAyE,QAAA,EAAO;UAAM;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAO,CAAC,eACrB7E,OAAA;YACEsF,IAAI,EAAC,MAAM;YACXpB,KAAK,EAAE9C,eAAe,CAACE,IAAK;YAC5BiE,QAAQ,EAAGC,CAAC,IAAKnE,kBAAkB,CAAC;cAAC,GAAGD,eAAe;cAAEE,IAAI,EAAEkE,CAAC,CAACC,MAAM,CAACvB;YAAK,CAAC,CAAE;YAChFwB,WAAW,EAAC,iBAAiB;YAC7BC,QAAQ;UAAA;YAAAjB,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACT,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACC,CAAC,eACN7E,OAAA;UAAKwE,SAAS,EAAC,YAAY;UAAAC,QAAA,gBACzBzE,OAAA;YAAAyE,QAAA,EAAO;UAAc;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAO,CAAC,eAC7B7E,OAAA;YACEsF,IAAI,EAAC,KAAK;YACVpB,KAAK,EAAE9C,eAAe,CAACG,KAAM;YAC7BgE,QAAQ,EAAGC,CAAC,IAAKnE,kBAAkB,CAAC;cAAC,GAAGD,eAAe;cAAEG,KAAK,EAAEiE,CAAC,CAACC,MAAM,CAACvB;YAAK,CAAC,CAAE;YACjFwB,WAAW,EAAC,yBAAyB;YACrCC,QAAQ;UAAA;YAAAjB,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACT,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACC,CAAC,eACN7E,OAAA;UAAKwE,SAAS,EAAC,YAAY;UAAAC,QAAA,gBACzBzE,OAAA;YAAO8E,KAAK,EAAE;cAACE,KAAK,EAAE,SAAS;cAAEY,UAAU,EAAE;YAAM,CAAE;YAAAnB,QAAA,EAAC;UAEtD;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAO,CAAC,eACR7E,OAAA;YACE6F,EAAE,EAAC,qBAAqB;YACxBH,WAAW,EAAC,uHAAuH;YACnII,IAAI,EAAC,GAAG;YACRhB,KAAK,EAAE;cACLiB,MAAM,EAAE,mBAAmB;cAC3BC,YAAY,EAAE,KAAK;cACnBC,OAAO,EAAE,MAAM;cACfC,QAAQ,EAAE;YACZ;UAAE;YAAAxB,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACH,CAAC,eACF7E,OAAA;YAAO8E,KAAK,EAAE;cAACE,KAAK,EAAE,SAAS;cAAEkB,QAAQ,EAAE;YAAQ,CAAE;YAAAzB,QAAA,EAAC;UAEtD;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAO,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACL,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH,CAAC,eAEN7E,OAAA;QAAKwE,SAAS,EAAC,kBAAkB;QAAAC,QAAA,gBAC/BzE,OAAA;UAAQwE,SAAS,EAAC,mBAAmB;UAACS,OAAO,EAAEA,CAAA,KAAM9D,cAAc,CAAC,MAAM,CAAE;UAAAsD,QAAA,EAAC;QAE7E;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAQ,CAAC,eACT7E,OAAA;UAAQwE,SAAS,EAAC,iBAAiB;UAACS,OAAO,EAAE5B,gBAAiB;UAAAoB,QAAA,EAAC;QAE/D;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAQ,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACN,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH,CAAC;EAEV;;EAEA;EACA,IAAI3D,WAAW,KAAK,SAAS,EAAE;IAC7B,oBACElB,OAAA;MAAKwE,SAAS,EAAC,eAAe;MAAAC,QAAA,gBAC5BzE,OAAA;QAAKwE,SAAS,EAAC,aAAa;QAAAC,QAAA,gBAC1BzE,OAAA;UAAAyE,QAAA,EAAI;QAAU;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAI,CAAC,eACnB7E,OAAA;UAAKwE,SAAS,EAAC,YAAY;UAAAC,QAAA,GAAC,qBAAS,EAAChE,OAAO;QAAA;UAAAiE,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAM,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACjD,CAAC,eAEN7E,OAAA;QAAKwE,SAAS,EAAC,iBAAiB;QAAAC,QAAA,gBAC9BzE,OAAA;UAAAyE,QAAA,EAAI;QAAa;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAI,CAAC,eACtB7E,OAAA;UAAKwE,SAAS,EAAC,eAAe;UAAAC,QAAA,gBAC5BzE,OAAA;YAAAyE,QAAA,gBAAGzE,OAAA;cAAAyE,QAAA,EAAQ;YAAK;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAQ,CAAC,KAAC,EAACzD,eAAe,CAACE,IAAI;UAAA;YAAAoD,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAI,CAAC,eACpD7E,OAAA;YAAAyE,QAAA,gBAAGzE,OAAA;cAAAyE,QAAA,EAAQ;YAAM;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAQ,CAAC,KAAC,EAACzD,eAAe,CAACG,KAAK;UAAA;YAAAmD,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAI,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACnD,CAAC,eACN7E,OAAA;UAAKwE,SAAS,EAAC,aAAa;UAAAC,QAAA,eAC1BzE,OAAA;YAAAyE,QAAA,GAAQ,iBAAe,EAAC3B,cAAc,CAAC,CAAC,CAACuC,OAAO,CAAC,CAAC,CAAC;UAAA;YAAAX,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAS;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAC1D,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH,CAAC,eAEN7E,OAAA;QAAKwE,SAAS,EAAC,iBAAiB;QAAAC,QAAA,gBAC9BzE,OAAA;UAAAyE,QAAA,EAAI;QAAqB;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAI,CAAC,eAC9B7E,OAAA;UAAKwE,SAAS,EAAC,iBAAiB;UAAAC,QAAA,gBAC9BzE,OAAA;YACEwE,SAAS,EAAC,4BAA4B;YACtCS,OAAO,EAAEA,CAAA,KAAM1B,cAAc,CAAC,QAAQ,CAAE;YACxC4C,QAAQ,EAAEzE,iBAAkB;YAAA+C,QAAA,EAE3B/C,iBAAiB,gBAChB1B,OAAA,CAAAE,SAAA;cAAAuE,QAAA,gBACEzE,OAAA;gBAAKwE,SAAS,EAAC,SAAS;gBAACM,KAAK,EAAE;kBAACsB,KAAK,EAAE,MAAM;kBAAEC,MAAM,EAAE,MAAM;kBAAEC,WAAW,EAAE;gBAAM;cAAE;gBAAA5B,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAM,CAAC,iBAE9F;YAAA,eAAE,CAAC,gBAEH7E,OAAA,CAAAE,SAAA;cAAAuE,QAAA,EAAE;YAAa,gBAAE;UAClB;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACK,CAAC,eACT7E,OAAA;YACEwE,SAAS,EAAC,0BAA0B;YACpCS,OAAO,EAAEA,CAAA,KAAM1B,cAAc,CAAC,MAAM,CAAE;YACtC4C,QAAQ,EAAEzE,iBAAkB;YAAA+C,QAAA,EAC7B;UAED;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAQ,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACN,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH,CAAC,eAEN7E,OAAA;QAAKwE,SAAS,EAAC,iBAAiB;QAAAC,QAAA,eAC9BzE,OAAA;UAAQwE,SAAS,EAAC,mBAAmB;UAACS,OAAO,EAAEA,CAAA,KAAM9D,cAAc,CAAC,UAAU,CAAE;UAAAsD,QAAA,EAAC;QAEjF;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAQ;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACN,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH,CAAC;EAEV;;EAEA;EACA,IAAI3D,WAAW,KAAK,YAAY,EAAE;IAChC,oBACElB,OAAA;MAAKwE,SAAS,EAAC,eAAe;MAAAC,QAAA,gBAC5BzE,OAAA;QAAKwE,SAAS,EAAC,aAAa;QAAAC,QAAA,gBAC1BzE,OAAA;UAAAyE,QAAA,EAAI;QAAkB;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAI,CAAC,eAC3B7E,OAAA;UAAKwE,SAAS,EAAC,YAAY;UAAAC,QAAA,GAAC,qBAAS,EAAChE,OAAO;QAAA;UAAAiE,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAM,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACjD,CAAC,eAEN7E,OAAA;QAAKwE,SAAS,EAAC,iBAAiB;QAAAC,QAAA,gBAC9BzE,OAAA;UAAKwE,SAAS,EAAC,iBAAiB;UAAAC,QAAA,EAAC;QAAC;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAK,CAAC,eACxC7E,OAAA;UAAAyE,QAAA,EAAI;QAAgC;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAI,CAAC,eACzC7E,OAAA;UAAAyE,QAAA,EAAG;QAAmF;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAG,CAAC,EACzFrD,SAAS,iBACRxB,OAAA;UAAKwE,SAAS,EAAC,eAAe;UAAAC,QAAA,gBAC5BzE,OAAA;YAAAyE,QAAA,gBAAGzE,OAAA;cAAAyE,QAAA,EAAQ;YAAa;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAQ,CAAC,KAAC,EAACrD,SAAS,CAAC8C,WAAW;UAAA;YAAAI,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAI,CAAC,eAC7D7E,OAAA;YAAAyE,QAAA,gBAAGzE,OAAA;cAAAyE,QAAA,EAAQ;YAAa;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAQ,CAAC,MAAE,EAACrD,SAAS,CAAC+E,WAAW,CAAClB,OAAO,CAAC,CAAC,CAAC;UAAA;YAAAX,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAI,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACtE,CACN,eACD7E,OAAA;UAAQwE,SAAS,EAAC,iBAAiB;UAACS,OAAO,EAAEV,UAAW;UAAAE,QAAA,EAAC;QAEzD;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAQ,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACN,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH,CAAC;EAEV;EAEA,oBACE7E,OAAA;IAAKwE,SAAS,EAAC,eAAe;IAAAC,QAAA,gBAC5BzE,OAAA;MAAKwE,SAAS,EAAC,aAAa;MAAAC,QAAA,gBAC1BzE,OAAA;QAAAyE,QAAA,EAAI;MAAmB;QAAAC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAI,CAAC,eAC5B7E,OAAA;QAAKwE,SAAS,EAAC,YAAY;QAAAC,QAAA,GAAC,qBACjB,EAAChE,OAAO;MAAA;QAAAiE,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACd,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH,CAAC,EAEL2B,MAAM,CAACC,IAAI,CAAC/F,IAAI,CAAC,CAACyC,MAAM,KAAK,CAAC,gBAC7BnD,OAAA;MAAKwE,SAAS,EAAC,MAAM;MAACM,KAAK,EAAE;QAACC,SAAS,EAAE;MAAQ,CAAE;MAAAN,QAAA,gBACjDzE,OAAA;QAAAyE,QAAA,EAAI;MAA2B;QAAAC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAI,CAAC,eACpC7E,OAAA;QAAAyE,QAAA,EAAG;MAAwD;QAAAC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAG,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAC5D,CAAC,GAEN2B,MAAM,CAACC,IAAI,CAAC/F,IAAI,CAAC,CAAC8B,GAAG,CAACkE,QAAQ,iBAC5B1G,OAAA;MAAoBwE,SAAS,EAAC,eAAe;MAAAC,QAAA,gBAC3CzE,OAAA;QAAKwE,SAAS,EAAC,iBAAiB;QAAAC,QAAA,eAC9BzE,OAAA;UAAAyE,QAAA,GACGiC,QAAQ,KAAK,YAAY,IAAI,IAAI,EACjCA,QAAQ,KAAK,OAAO,IAAI,KAAK,EAC7BA,QAAQ,KAAK,UAAU,IAAI,IAAI,EAC/BA,QAAQ,KAAK,WAAW,IAAI,IAAI,EAChCA,QAAQ,CAACC,MAAM,CAAC,CAAC,CAAC,CAACC,WAAW,CAAC,CAAC,GAAGF,QAAQ,CAACG,KAAK,CAAC,CAAC,CAAC;QAAA;UAAAnC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACnD;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACF,CAAC,eACN7E,OAAA;QAAKwE,SAAS,EAAC,YAAY;QAAAC,QAAA,EACxB/D,IAAI,CAACgG,QAAQ,CAAC,CAAClE,GAAG,CAACL,IAAI,iBACtBnC,OAAA;UAAoBwE,SAAS,EAAC,WAAW;UAAAC,QAAA,gBACvCzE,OAAA;YAAAyE,QAAA,EAAKtC,IAAI,CAACb;UAAI;YAAAoD,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAK,CAAC,eACpB7E,OAAA;YAAAyE,QAAA,EAAItC,IAAI,CAAC2E;UAAW;YAAApC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAI,CAAC,eACzB7E,OAAA;YAAKwE,SAAS,EAAC,OAAO;YAAAC,QAAA,GAAC,GAAC,EAACtC,IAAI,CAACc,KAAK,CAACoC,OAAO,CAAC,CAAC,CAAC;UAAA;YAAAX,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAM,CAAC,eACrD7E,OAAA;YACEwE,SAAS,EAAC,iBAAiB;YAC3BS,OAAO,EAAEA,CAAA,KAAM/C,SAAS,CAACC,IAAI,CAAE;YAAAsC,QAAA,EAChC;UAED;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAQ,CAAC;QAAA,GATD1C,IAAI,CAACI,GAAG;UAAAmC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAUb,CACN;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACC,CAAC;IAAA,GAxBE6B,QAAQ;MAAAhC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAyBb,CACN,CACF,EAEAjE,IAAI,CAACuC,MAAM,GAAG,CAAC,iBACdnD,OAAA;MAAKwE,SAAS,EAAC,MAAM;MAAAC,QAAA,gBACnBzE,OAAA;QAAKwE,SAAS,EAAC,aAAa;QAAAC,QAAA,gBAC1BzE,OAAA;UAAAyE,QAAA,EAAI;QAAa;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAI,CAAC,eACtB7E,OAAA;UAAKwE,SAAS,EAAC,YAAY;UAAAC,QAAA,GAAC,GACzB,EAAC3B,cAAc,CAAC,CAAC,CAACuC,OAAO,CAAC,CAAC,CAAC;QAAA;UAAAX,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAC1B,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH,CAAC,EAELjE,IAAI,CAAC4B,GAAG,CAACL,IAAI,iBACZnC,OAAA;QAAoBwE,SAAS,EAAC,WAAW;QAAAC,QAAA,gBACvCzE,OAAA;UAAKwE,SAAS,EAAC,gBAAgB;UAAAC,QAAA,gBAC7BzE,OAAA;YAAAyE,QAAA,EAAKtC,IAAI,CAACb;UAAI;YAAAoD,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAK,CAAC,eACpB7E,OAAA;YAAKwE,SAAS,EAAC,iBAAiB;YAAAC,QAAA,GAAC,GAAC,EAACtC,IAAI,CAACc,KAAK,CAACoC,OAAO,CAAC,CAAC,CAAC,EAAC,OAAK;UAAA;YAAAX,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAK,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACjE,CAAC,eACN7E,OAAA;UAAKwE,SAAS,EAAC,mBAAmB;UAAAC,QAAA,gBAChCzE,OAAA;YACEwE,SAAS,EAAC,cAAc;YACxBS,OAAO,EAAEA,CAAA,KAAMpC,cAAc,CAACV,IAAI,CAACI,GAAG,EAAEJ,IAAI,CAACM,QAAQ,GAAG,CAAC,CAAE;YAAAgC,QAAA,EAC5D;UAED;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAQ,CAAC,eACT7E,OAAA;YAAMwE,SAAS,EAAC,UAAU;YAAAC,QAAA,EAAEtC,IAAI,CAACM;UAAQ;YAAAiC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAO,CAAC,eACjD7E,OAAA;YACEwE,SAAS,EAAC,cAAc;YACxBS,OAAO,EAAEA,CAAA,KAAMpC,cAAc,CAACV,IAAI,CAACI,GAAG,EAAEJ,IAAI,CAACM,QAAQ,GAAG,CAAC,CAAE;YAAAgC,QAAA,EAC5D;UAED;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAQ,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACN,CAAC;MAAA,GAnBE1C,IAAI,CAACI,GAAG;QAAAmC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAoBb,CACN,CAAC,eAEF7E,OAAA;QAAKwE,SAAS,EAAC,cAAc;QAAAC,QAAA,eAC3BzE,OAAA;UACEwE,SAAS,EAAC,cAAc;UACxBS,OAAO,EAAE/B,iBAAkB;UAAAuB,QAAA,GAC5B,sCAC2B,EAAC3B,cAAc,CAAC,CAAC,CAACuC,OAAO,CAAC,CAAC,CAAC;QAAA;UAAAX,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAChD;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACN,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH,CACN;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACE,CAAC;AAEV,CAAC;AAACrE,EAAA,CA1ZID,YAAY;EAAA,QACIV,SAAS;AAAA;AAAAkH,EAAA,GADzBxG,YAAY;AA4ZlB,eAAeA,YAAY;AAAC,IAAAwG,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}