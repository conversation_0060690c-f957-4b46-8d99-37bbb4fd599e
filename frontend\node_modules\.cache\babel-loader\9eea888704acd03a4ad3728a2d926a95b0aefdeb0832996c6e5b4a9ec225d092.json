{"ast": null, "code": "var _jsxFileName = \"C:\\\\restraunat managment system\\\\frontend\\\\src\\\\components\\\\ManagerDashboard.js\",\n  _s = $RefreshSig$();\nimport React, { useState, useEffect } from 'react';\nimport axios from 'axios';\nimport io from 'socket.io-client';\nimport { jsxDEV as _jsxDEV, Fragment as _Fragment } from \"react/jsx-dev-runtime\";\nconst API_BASE_URL = process.env.REACT_APP_API_BASE_URL;\nconst SOCKET_URL = process.env.REACT_APP_SOCKET_URL;\nconst ManagerDashboard = ({\n  user,\n  onLogout\n}) => {\n  _s();\n  const [activeTab, setActiveTab] = useState('orders');\n  const [orders, setOrders] = useState([]);\n  const [tables, setTables] = useState([]);\n  const [menuItems, setMenuItems] = useState([]);\n  const [cashPendingOrders, setCashPendingOrders] = useState([]);\n  const [socket, setSocket] = useState(null);\n  const [loading, setLoading] = useState(true);\n\n  // Menu management states\n  const [editingItem, setEditingItem] = useState(null);\n  const [newItem, setNewItem] = useState({\n    name: '',\n    description: '',\n    price: '',\n    category: 'mains',\n    available: true\n  });\n  const [showAddForm, setShowAddForm] = useState(false);\n  useEffect(() => {\n    fetchOrders();\n    fetchTables();\n    fetchMenuItems();\n    fetchCashPendingOrders();\n    const newSocket = io(SOCKET_URL);\n    setSocket(newSocket);\n    newSocket.on('newOrder', order => {\n      setOrders(prev => [order, ...prev]);\n    });\n    newSocket.on('orderStatusUpdate', updatedOrder => {\n      setOrders(prev => prev.map(order => order._id === updatedOrder._id ? updatedOrder : order));\n    });\n    newSocket.on('tableStatusUpdate', updatedTable => {\n      setTables(prev => prev.map(table => table._id === updatedTable._id ? updatedTable : table));\n    });\n    newSocket.on('cashPaymentPending', order => {\n      setCashPendingOrders(prev => [order, ...prev]);\n    });\n    newSocket.on('cashPaymentConfirmed', order => {\n      setCashPendingOrders(prev => prev.filter(o => o._id !== order._id));\n    });\n    return () => newSocket.close();\n  }, []);\n  const getAuthHeaders = () => {\n    const token = localStorage.getItem('token');\n    return {\n      headers: {\n        'Authorization': `Bearer ${token}`\n      }\n    };\n  };\n  const fetchOrders = async () => {\n    try {\n      const response = await axios.get(`${API_BASE_URL}/api/manager/orders`, getAuthHeaders());\n      setOrders(response.data);\n      setLoading(false);\n    } catch (error) {\n      var _error$response;\n      console.error('Error fetching orders:', error);\n      if (((_error$response = error.response) === null || _error$response === void 0 ? void 0 : _error$response.status) === 401) {\n        onLogout();\n      }\n      setLoading(false);\n    }\n  };\n  const fetchTables = async () => {\n    try {\n      const response = await axios.get(`${API_BASE_URL}/api/manager/tables`, getAuthHeaders());\n      setTables(response.data);\n    } catch (error) {\n      console.error('Error fetching tables:', error);\n    }\n  };\n  const fetchMenuItems = async () => {\n    try {\n      const response = await axios.get(`${API_BASE_URL}/api/manager/menu`, getAuthHeaders());\n      setMenuItems(response.data);\n    } catch (error) {\n      var _error$response2;\n      console.error('Error fetching menu items:', error);\n      if (((_error$response2 = error.response) === null || _error$response2 === void 0 ? void 0 : _error$response2.status) === 401) {\n        onLogout();\n      }\n    }\n  };\n  const fetchCashPendingOrders = async () => {\n    try {\n      const response = await axios.get(`${API_BASE_URL}/api/manager/orders/cash-pending`, getAuthHeaders());\n      setCashPendingOrders(response.data);\n    } catch (error) {\n      console.error('Error fetching cash pending orders:', error);\n    }\n  };\n  const updateOrderStatus = async (orderId, status) => {\n    try {\n      await axios.patch(`${API_BASE_URL}/api/manager/orders/${orderId}/status`, {\n        status\n      }, getAuthHeaders());\n    } catch (error) {\n      console.error('Error updating order status:', error);\n      alert('Error updating order status. Please try again.');\n    }\n  };\n  const updateTableStatus = async (tableId, status) => {\n    try {\n      await axios.patch(`${API_BASE_URL}/api/manager/tables/${tableId}/status`, {\n        status\n      }, getAuthHeaders());\n      fetchTables();\n    } catch (error) {\n      console.error('Error updating table status:', error);\n    }\n  };\n  const confirmCashPayment = async orderId => {\n    try {\n      await axios.post(`${API_BASE_URL}/api/orders/${orderId}/payment/cash/confirm`, {}, getAuthHeaders());\n      fetchCashPendingOrders();\n      fetchOrders();\n      fetchTables();\n    } catch (error) {\n      console.error('Error confirming cash payment:', error);\n      alert('Error confirming cash payment. Please try again.');\n    }\n  };\n  const renderOrders = () => /*#__PURE__*/_jsxDEV(\"div\", {\n    className: \"orders-section\",\n    children: [/*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"section-header\",\n      children: [/*#__PURE__*/_jsxDEV(\"h2\", {\n        children: [\"\\uD83D\\uDCCB Orders (\", orders.length, \")\"]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 162,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n        style: {\n          color: '#666',\n          fontSize: '0.9rem'\n        },\n        children: \"You can only mark ready orders as served\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 163,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 161,\n      columnNumber: 7\n    }, this), orders.map(order => /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"order-card\",\n      children: [/*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"order-header\",\n        children: [/*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"order-info\",\n          children: [/*#__PURE__*/_jsxDEV(\"h3\", {\n            children: [\"Order #\", order.orderNumber]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 170,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"order-meta\",\n            children: [/*#__PURE__*/_jsxDEV(\"span\", {\n              className: \"table-info\",\n              children: [\"\\uD83D\\uDCCD Table \", order.table.tableNumber]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 172,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n              className: \"order-time\",\n              children: [\"\\uD83D\\uDD52 \", new Date(order.createdAt).toLocaleTimeString()]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 173,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n              className: \"customer-info\",\n              children: [\"\\uD83D\\uDC64 \", order.customerName]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 174,\n              columnNumber: 17\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 171,\n            columnNumber: 15\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 169,\n          columnNumber: 13\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"order-status\",\n          children: /*#__PURE__*/_jsxDEV(\"span\", {\n            className: `status ${order.status}`,\n            children: order.status.toUpperCase()\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 178,\n            columnNumber: 15\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 177,\n          columnNumber: 13\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 168,\n        columnNumber: 11\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"order-items\",\n        children: order.items.map((item, index) => /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"order-item\",\n          children: [/*#__PURE__*/_jsxDEV(\"span\", {\n            children: [item.quantity, \"x \", item.menuItem.name]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 187,\n            columnNumber: 17\n          }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n            children: [\"$\", (item.price * item.quantity).toFixed(2)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 188,\n            columnNumber: 17\n          }, this)]\n        }, index, true, {\n          fileName: _jsxFileName,\n          lineNumber: 186,\n          columnNumber: 15\n        }, this))\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 184,\n        columnNumber: 11\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"order-total\",\n        children: [\"Total: $\", order.totalAmount.toFixed(2)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 193,\n        columnNumber: 11\n      }, this), order.specialInstructions && order.specialInstructions.trim() !== '' ? /*#__PURE__*/_jsxDEV(\"div\", {\n        style: {\n          marginTop: '10px',\n          padding: '12px',\n          background: '#fff3cd',\n          borderRadius: '8px',\n          border: '2px solid #ffc107'\n        },\n        children: [/*#__PURE__*/_jsxDEV(\"strong\", {\n          children: \"\\uD83D\\uDCDD Special Instructions:\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 205,\n          columnNumber: 15\n        }, this), \" \", order.specialInstructions]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 198,\n        columnNumber: 13\n      }, this) : /*#__PURE__*/_jsxDEV(\"div\", {\n        style: {\n          marginTop: '10px',\n          padding: '8px',\n          background: '#f8f9fa',\n          borderRadius: '6px',\n          color: '#6c757d',\n          fontSize: '0.9rem',\n          fontStyle: 'italic'\n        },\n        children: \"\\uD83D\\uDCDD No special instructions\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 208,\n        columnNumber: 13\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"status-buttons\",\n        children: [order.status === 'ready' && /*#__PURE__*/_jsxDEV(\"button\", {\n          className: \"btn-serve\",\n          onClick: () => updateOrderStatus(order._id, 'served'),\n          children: \"\\uD83C\\uDF7D\\uFE0F Mark as Served\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 223,\n          columnNumber: 15\n        }, this), order.status !== 'ready' && order.status !== 'served' && /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"status-info\",\n          children: \"\\u23F3 Waiting for kitchen to mark as ready\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 231,\n          columnNumber: 15\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 221,\n        columnNumber: 11\n      }, this)]\n    }, order._id, true, {\n      fileName: _jsxFileName,\n      lineNumber: 167,\n      columnNumber: 9\n    }, this))]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 160,\n    columnNumber: 5\n  }, this);\n  const renderCashPayments = () => /*#__PURE__*/_jsxDEV(\"div\", {\n    className: \"cash-payments-section\",\n    children: [/*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"section-header\",\n      children: [/*#__PURE__*/_jsxDEV(\"h2\", {\n        children: [\"\\uD83D\\uDCB0 Cash Payment Confirmations (\", cashPendingOrders.length, \")\"]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 244,\n        columnNumber: 9\n      }, this), cashPendingOrders.length === 0 && /*#__PURE__*/_jsxDEV(\"p\", {\n        style: {\n          color: '#666',\n          fontStyle: 'italic'\n        },\n        children: \"No pending cash payments\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 246,\n        columnNumber: 11\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 243,\n      columnNumber: 7\n    }, this), cashPendingOrders.map(order => /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"cash-payment-card\",\n      children: [/*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"order-header\",\n        children: [/*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"order-info\",\n          children: [/*#__PURE__*/_jsxDEV(\"h3\", {\n            children: [\"Order #\", order.orderNumber]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 254,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"order-meta\",\n            children: [/*#__PURE__*/_jsxDEV(\"span\", {\n              className: \"table-info\",\n              children: [\"\\uD83D\\uDCCD Table \", order.table.tableNumber]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 256,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n              className: \"order-time\",\n              children: [\"\\uD83D\\uDD52 \", new Date(order.createdAt).toLocaleTimeString()]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 257,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n              className: \"payment-method\",\n              children: \"\\uD83D\\uDCB0 Cash Payment\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 258,\n              columnNumber: 17\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 255,\n            columnNumber: 15\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 253,\n          columnNumber: 13\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"order-total\",\n          children: /*#__PURE__*/_jsxDEV(\"strong\", {\n            children: [\"$\", order.totalAmount.toFixed(2)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 262,\n            columnNumber: 15\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 261,\n          columnNumber: 13\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 252,\n        columnNumber: 11\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"customer-details\",\n        children: [/*#__PURE__*/_jsxDEV(\"p\", {\n          children: [/*#__PURE__*/_jsxDEV(\"strong\", {\n            children: \"Customer:\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 267,\n            columnNumber: 16\n          }, this), \" \", order.customerName]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 267,\n          columnNumber: 13\n        }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n          children: [/*#__PURE__*/_jsxDEV(\"strong\", {\n            children: \"Phone:\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 268,\n            columnNumber: 16\n          }, this), \" \", order.customerPhone]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 268,\n          columnNumber: 13\n        }, this), order.specialInstructions && order.specialInstructions.trim() !== '' ? /*#__PURE__*/_jsxDEV(\"div\", {\n          style: {\n            marginTop: '10px',\n            padding: '12px',\n            background: '#fff3cd',\n            borderRadius: '8px',\n            border: '2px solid #ffc107'\n          },\n          children: [/*#__PURE__*/_jsxDEV(\"strong\", {\n            children: \"\\uD83D\\uDCDD Special Instructions:\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 277,\n            columnNumber: 17\n          }, this), \" \", order.specialInstructions]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 270,\n          columnNumber: 15\n        }, this) : /*#__PURE__*/_jsxDEV(\"div\", {\n          style: {\n            marginTop: '10px',\n            padding: '8px',\n            background: '#f8f9fa',\n            borderRadius: '6px',\n            color: '#6c757d',\n            fontSize: '0.9rem',\n            fontStyle: 'italic'\n          },\n          children: \"\\uD83D\\uDCDD No special instructions\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 280,\n          columnNumber: 15\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 266,\n        columnNumber: 11\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"order-items\",\n        children: [/*#__PURE__*/_jsxDEV(\"h4\", {\n          children: \"Items:\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 295,\n          columnNumber: 13\n        }, this), order.items.map((item, index) => /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"order-item\",\n          children: [/*#__PURE__*/_jsxDEV(\"span\", {\n            children: [item.quantity, \"x \", item.menuItem.name]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 298,\n            columnNumber: 17\n          }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n            children: [\"$\", (item.price * item.quantity).toFixed(2)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 299,\n            columnNumber: 17\n          }, this)]\n        }, index, true, {\n          fileName: _jsxFileName,\n          lineNumber: 297,\n          columnNumber: 15\n        }, this))]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 294,\n        columnNumber: 11\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"cash-payment-actions\",\n        children: /*#__PURE__*/_jsxDEV(\"button\", {\n          className: \"btn-confirm-cash\",\n          onClick: () => confirmCashPayment(order._id),\n          children: \"\\u2705 Confirm Cash Payment\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 305,\n          columnNumber: 13\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 304,\n        columnNumber: 11\n      }, this)]\n    }, order._id, true, {\n      fileName: _jsxFileName,\n      lineNumber: 251,\n      columnNumber: 9\n    }, this))]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 242,\n    columnNumber: 5\n  }, this);\n  const renderTables = () => /*#__PURE__*/_jsxDEV(\"div\", {\n    className: \"tables-section\",\n    children: [/*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"section-header\",\n      children: /*#__PURE__*/_jsxDEV(\"h2\", {\n        children: [\"\\uD83E\\uDE91 Table Management (\", tables.length, \" tables)\"]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 320,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 319,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"tables-grid\",\n      children: tables.map(table => /*#__PURE__*/_jsxDEV(\"div\", {\n        className: `table-card ${table.status}`,\n        children: [/*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"table-header\",\n          children: [/*#__PURE__*/_jsxDEV(\"h3\", {\n            children: [\"Table \", table.tableNumber]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 327,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n            className: `table-status ${table.status}`,\n            children: [table.status === 'available' && '✅ Available', table.status === 'occupied' && '🔴 Occupied', table.status === 'reserved' && '🟡 Reserved', table.status === 'maintenance' && '🔧 Maintenance']\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 328,\n            columnNumber: 15\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 326,\n          columnNumber: 13\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"table-info\",\n          children: [/*#__PURE__*/_jsxDEV(\"p\", {\n            children: [/*#__PURE__*/_jsxDEV(\"strong\", {\n              children: \"Capacity:\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 337,\n              columnNumber: 18\n            }, this), \" \", table.capacity, \" people\"]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 337,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n            children: [/*#__PURE__*/_jsxDEV(\"strong\", {\n              children: \"QR Code:\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 338,\n              columnNumber: 18\n            }, this), \" Generated\"]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 338,\n            columnNumber: 15\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 336,\n          columnNumber: 13\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"table-actions\",\n          children: /*#__PURE__*/_jsxDEV(\"select\", {\n            value: table.status,\n            onChange: e => updateTableStatus(table._id, e.target.value),\n            className: \"status-select\",\n            children: [/*#__PURE__*/_jsxDEV(\"option\", {\n              value: \"available\",\n              children: \"Available\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 347,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(\"option\", {\n              value: \"occupied\",\n              children: \"Occupied\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 348,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(\"option\", {\n              value: \"reserved\",\n              children: \"Reserved\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 349,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(\"option\", {\n              value: \"maintenance\",\n              children: \"Maintenance\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 350,\n              columnNumber: 17\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 342,\n            columnNumber: 15\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 341,\n          columnNumber: 13\n        }, this)]\n      }, table._id, true, {\n        fileName: _jsxFileName,\n        lineNumber: 325,\n        columnNumber: 11\n      }, this))\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 323,\n      columnNumber: 7\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 318,\n    columnNumber: 5\n  }, this);\n  const handleAddItem = async e => {\n    e.preventDefault();\n    try {\n      await axios.post(`${API_BASE_URL}/api/manager/menu`, {\n        ...newItem,\n        price: parseFloat(newItem.price)\n      }, getAuthHeaders());\n      setNewItem({\n        name: '',\n        description: '',\n        price: '',\n        category: 'mains',\n        available: true\n      });\n      setShowAddForm(false);\n      fetchMenuItems();\n    } catch (error) {\n      console.error('Error adding menu item:', error);\n      alert('Error adding menu item. Please try again.');\n    }\n  };\n  const handleUpdateItem = async (itemId, updatedData) => {\n    try {\n      await axios.put(`${API_BASE_URL}/api/manager/menu/${itemId}`, updatedData, getAuthHeaders());\n      setEditingItem(null);\n      fetchMenuItems();\n    } catch (error) {\n      console.error('Error updating menu item:', error);\n      alert('Error updating menu item. Please try again.');\n    }\n  };\n  const handleDeleteItem = async itemId => {\n    if (window.confirm('Are you sure you want to delete this menu item?')) {\n      try {\n        await axios.delete(`${API_BASE_URL}/api/manager/menu/${itemId}`, getAuthHeaders());\n        fetchMenuItems();\n      } catch (error) {\n        console.error('Error deleting menu item:', error);\n        alert('Error deleting menu item. Please try again.');\n      }\n    }\n  };\n  const renderMenuManagement = () => {\n    const groupedItems = menuItems.reduce((acc, item) => {\n      if (!acc[item.category]) acc[item.category] = [];\n      acc[item.category].push(item);\n      return acc;\n    }, {});\n    return /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"menu-management-section\",\n      children: [/*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"section-header\",\n        children: [/*#__PURE__*/_jsxDEV(\"h2\", {\n          children: [\"\\uD83D\\uDCCB Menu Management (\", menuItems.length, \" items)\"]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 416,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n          className: \"btn-add-item\",\n          onClick: () => setShowAddForm(!showAddForm),\n          children: showAddForm ? '❌ Cancel' : '➕ Add New Item'\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 417,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 415,\n        columnNumber: 9\n      }, this), showAddForm && /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"add-item-form\",\n        children: [/*#__PURE__*/_jsxDEV(\"h3\", {\n          children: \"Add New Menu Item\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 427,\n          columnNumber: 13\n        }, this), /*#__PURE__*/_jsxDEV(\"form\", {\n          onSubmit: handleAddItem,\n          children: [/*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"form-row\",\n            children: [/*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"form-group\",\n              children: [/*#__PURE__*/_jsxDEV(\"label\", {\n                children: \"Name *\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 431,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(\"input\", {\n                type: \"text\",\n                value: newItem.name,\n                onChange: e => setNewItem({\n                  ...newItem,\n                  name: e.target.value\n                }),\n                required: true\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 432,\n                columnNumber: 19\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 430,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"form-group\",\n              children: [/*#__PURE__*/_jsxDEV(\"label\", {\n                children: \"Price *\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 440,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(\"input\", {\n                type: \"number\",\n                step: \"0.01\",\n                value: newItem.price,\n                onChange: e => setNewItem({\n                  ...newItem,\n                  price: e.target.value\n                }),\n                required: true\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 441,\n                columnNumber: 19\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 439,\n              columnNumber: 17\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 429,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"form-group\",\n            children: [/*#__PURE__*/_jsxDEV(\"label\", {\n              children: \"Category *\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 451,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(\"select\", {\n              value: newItem.category,\n              onChange: e => setNewItem({\n                ...newItem,\n                category: e.target.value\n              }),\n              children: [/*#__PURE__*/_jsxDEV(\"option\", {\n                value: \"appetizers\",\n                children: \"Appetizers\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 456,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(\"option\", {\n                value: \"mains\",\n                children: \"Main Courses\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 457,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(\"option\", {\n                value: \"desserts\",\n                children: \"Desserts\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 458,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(\"option\", {\n                value: \"beverages\",\n                children: \"Beverages\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 459,\n                columnNumber: 19\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 452,\n              columnNumber: 17\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 450,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"form-group\",\n            children: [/*#__PURE__*/_jsxDEV(\"label\", {\n              children: \"Description\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 463,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(\"textarea\", {\n              value: newItem.description,\n              onChange: e => setNewItem({\n                ...newItem,\n                description: e.target.value\n              }),\n              rows: \"3\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 464,\n              columnNumber: 17\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 462,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"form-actions\",\n            children: [/*#__PURE__*/_jsxDEV(\"button\", {\n              type: \"submit\",\n              className: \"btn-save\",\n              children: \"Save Item\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 471,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n              type: \"button\",\n              onClick: () => setShowAddForm(false),\n              className: \"btn-cancel\",\n              children: \"Cancel\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 472,\n              columnNumber: 17\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 470,\n            columnNumber: 15\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 428,\n          columnNumber: 13\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 426,\n        columnNumber: 11\n      }, this), Object.keys(groupedItems).map(category => /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"menu-category-section\",\n        children: [/*#__PURE__*/_jsxDEV(\"h3\", {\n          className: \"category-title\",\n          children: [category === 'appetizers' && '🥗', category === 'mains' && '🍽️', category === 'desserts' && '🍰', category === 'beverages' && '🥤', category.charAt(0).toUpperCase() + category.slice(1), \" (\", groupedItems[category].length, \")\"]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 480,\n          columnNumber: 13\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"menu-items-grid\",\n          children: groupedItems[category].map(item => /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"menu-item-card\",\n            children: editingItem === item._id ? /*#__PURE__*/_jsxDEV(EditItemForm, {\n              item: item,\n              onSave: data => handleUpdateItem(item._id, data),\n              onCancel: () => setEditingItem(null)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 492,\n              columnNumber: 21\n            }, this) : /*#__PURE__*/_jsxDEV(_Fragment, {\n              children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"item-header\",\n                children: [/*#__PURE__*/_jsxDEV(\"h4\", {\n                  children: item.name\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 500,\n                  columnNumber: 25\n                }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                  className: \"item-price\",\n                  children: [\"$\", item.price.toFixed(2)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 501,\n                  columnNumber: 25\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 499,\n                columnNumber: 23\n              }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n                className: \"item-description\",\n                children: item.description\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 503,\n                columnNumber: 23\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"item-status\",\n                children: /*#__PURE__*/_jsxDEV(\"span\", {\n                  className: `availability ${item.available ? 'available' : 'unavailable'}`,\n                  children: item.available ? '✅ Available' : '❌ Unavailable'\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 505,\n                  columnNumber: 25\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 504,\n                columnNumber: 23\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"item-actions\",\n                children: [/*#__PURE__*/_jsxDEV(\"button\", {\n                  className: \"btn-edit\",\n                  onClick: () => setEditingItem(item._id),\n                  children: \"\\u270F\\uFE0F Edit\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 510,\n                  columnNumber: 25\n                }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n                  className: \"btn-delete\",\n                  onClick: () => handleDeleteItem(item._id),\n                  children: \"\\uD83D\\uDDD1\\uFE0F Delete\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 516,\n                  columnNumber: 25\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 509,\n                columnNumber: 23\n              }, this)]\n            }, void 0, true)\n          }, item._id, false, {\n            fileName: _jsxFileName,\n            lineNumber: 490,\n            columnNumber: 17\n          }, this))\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 488,\n          columnNumber: 13\n        }, this)]\n      }, category, true, {\n        fileName: _jsxFileName,\n        lineNumber: 479,\n        columnNumber: 11\n      }, this))]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 414,\n      columnNumber: 7\n    }, this);\n  };\n\n  // EditItemForm component - moved outside to avoid hooks issues\n  const EditItemForm = ({\n    item,\n    onSave,\n    onCancel\n  }) => {\n    // Use a simple form without useState to avoid hooks issues\n    const handleSubmit = e => {\n      e.preventDefault();\n      const formData = new FormData(e.target);\n      const updatedData = {\n        name: formData.get('name'),\n        description: formData.get('description'),\n        price: parseFloat(formData.get('price')),\n        category: formData.get('category'),\n        available: formData.get('available') === 'on'\n      };\n      onSave(updatedData);\n    };\n    return /*#__PURE__*/_jsxDEV(\"form\", {\n      onSubmit: handleSubmit,\n      className: \"edit-item-form\",\n      children: [/*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"form-group\",\n        children: /*#__PURE__*/_jsxDEV(\"input\", {\n          type: \"text\",\n          name: \"name\",\n          defaultValue: item.name,\n          required: true,\n          placeholder: \"Item name\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 553,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 552,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"form-group\",\n        children: /*#__PURE__*/_jsxDEV(\"input\", {\n          type: \"number\",\n          name: \"price\",\n          step: \"0.01\",\n          defaultValue: item.price,\n          required: true,\n          placeholder: \"Price\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 562,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 561,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"form-group\",\n        children: /*#__PURE__*/_jsxDEV(\"select\", {\n          name: \"category\",\n          defaultValue: item.category,\n          children: [/*#__PURE__*/_jsxDEV(\"option\", {\n            value: \"appetizers\",\n            children: \"Appetizers\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 573,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"option\", {\n            value: \"mains\",\n            children: \"Main Courses\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 574,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"option\", {\n            value: \"desserts\",\n            children: \"Desserts\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 575,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"option\", {\n            value: \"beverages\",\n            children: \"Beverages\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 576,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 572,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 571,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"form-group\",\n        children: /*#__PURE__*/_jsxDEV(\"textarea\", {\n          name: \"description\",\n          defaultValue: item.description,\n          rows: \"2\",\n          placeholder: \"Description\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 580,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 579,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"form-group\",\n        children: /*#__PURE__*/_jsxDEV(\"label\", {\n          children: [/*#__PURE__*/_jsxDEV(\"input\", {\n            type: \"checkbox\",\n            name: \"available\",\n            defaultChecked: item.available\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 589,\n            columnNumber: 13\n          }, this), \"Available\"]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 588,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 587,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"form-actions\",\n        children: [/*#__PURE__*/_jsxDEV(\"button\", {\n          type: \"submit\",\n          className: \"btn-save\",\n          children: \"Save\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 598,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n          type: \"button\",\n          onClick: onCancel,\n          className: \"btn-cancel\",\n          children: \"Cancel\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 599,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 597,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 551,\n      columnNumber: 7\n    }, this);\n  };\n  if (loading) {\n    return /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"manager-dashboard\",\n      children: /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"loading\",\n        children: [/*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"spinner\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 609,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n          children: \"Loading manager dashboard...\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 610,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 608,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 607,\n      columnNumber: 7\n    }, this);\n  }\n  return /*#__PURE__*/_jsxDEV(\"div\", {\n    className: \"manager-dashboard\",\n    children: [/*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"manager-header\",\n      children: [/*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"header-left\",\n        children: [/*#__PURE__*/_jsxDEV(\"h1\", {\n          children: \"\\uD83D\\uDC68\\u200D\\uD83D\\uDCBC Manager Dashboard\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 620,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n          children: [\"Welcome, \", user.name]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 621,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 619,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"header-right\",\n        children: /*#__PURE__*/_jsxDEV(\"button\", {\n          className: \"logout-btn\",\n          onClick: onLogout,\n          children: \"\\uD83D\\uDEAA Logout\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 624,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 623,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 618,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"admin-tabs\",\n      children: [/*#__PURE__*/_jsxDEV(\"button\", {\n        className: activeTab === 'orders' ? 'active' : '',\n        onClick: () => setActiveTab('orders'),\n        children: \"Orders\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 631,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n        className: activeTab === 'cash-payments' ? 'active' : '',\n        onClick: () => setActiveTab('cash-payments'),\n        children: [\"Cash Payments \", cashPendingOrders.length > 0 && /*#__PURE__*/_jsxDEV(\"span\", {\n          className: \"notification-badge\",\n          children: cashPendingOrders.length\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 641,\n          columnNumber: 58\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 637,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n        className: activeTab === 'tables' ? 'active' : '',\n        onClick: () => setActiveTab('tables'),\n        children: \"Tables\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 643,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n        className: activeTab === 'menu' ? 'active' : '',\n        onClick: () => setActiveTab('menu'),\n        children: \"Menu Management\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 649,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 630,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"tab-content\",\n      children: [activeTab === 'orders' && renderOrders(), activeTab === 'cash-payments' && renderCashPayments(), activeTab === 'tables' && renderTables(), activeTab === 'menu' && renderMenuManagement()]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 657,\n      columnNumber: 7\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 617,\n    columnNumber: 5\n  }, this);\n};\n_s(ManagerDashboard, \"F87KHhZ50rFxOSbdjNsOgYvs8uI=\");\n_c = ManagerDashboard;\nexport default ManagerDashboard;\nvar _c;\n$RefreshReg$(_c, \"ManagerDashboard\");", "map": {"version": 3, "names": ["React", "useState", "useEffect", "axios", "io", "jsxDEV", "_jsxDEV", "Fragment", "_Fragment", "API_BASE_URL", "process", "env", "REACT_APP_API_BASE_URL", "SOCKET_URL", "REACT_APP_SOCKET_URL", "ManagerDashboard", "user", "onLogout", "_s", "activeTab", "setActiveTab", "orders", "setOrders", "tables", "setTables", "menuItems", "setMenuItems", "cashPendingOrders", "setCashPendingOrders", "socket", "setSocket", "loading", "setLoading", "editingItem", "setEditingItem", "newItem", "setNewItem", "name", "description", "price", "category", "available", "showAddForm", "setShowAddForm", "fetchOrders", "fetchTables", "fetchMenuItems", "fetchCashPendingOrders", "newSocket", "on", "order", "prev", "updatedOrder", "map", "_id", "updatedTable", "table", "filter", "o", "close", "getAuthHeaders", "token", "localStorage", "getItem", "headers", "response", "get", "data", "error", "_error$response", "console", "status", "_error$response2", "updateOrderStatus", "orderId", "patch", "alert", "updateTableStatus", "tableId", "confirmCashPayment", "post", "renderOrders", "className", "children", "length", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "style", "color", "fontSize", "orderNumber", "tableNumber", "Date", "createdAt", "toLocaleTimeString", "customerName", "toUpperCase", "items", "item", "index", "quantity", "menuItem", "toFixed", "totalAmount", "specialInstructions", "trim", "marginTop", "padding", "background", "borderRadius", "border", "fontStyle", "onClick", "renderCashPayments", "customerPhone", "renderTables", "capacity", "value", "onChange", "e", "target", "handleAddItem", "preventDefault", "parseFloat", "handleUpdateItem", "itemId", "updatedData", "put", "handleDeleteItem", "window", "confirm", "delete", "renderMenuManagement", "groupedItems", "reduce", "acc", "push", "onSubmit", "type", "required", "step", "rows", "Object", "keys", "char<PERSON>t", "slice", "EditItemForm", "onSave", "onCancel", "handleSubmit", "formData", "FormData", "defaultValue", "placeholder", "defaultChecked", "_c", "$RefreshReg$"], "sources": ["C:/restraunat managment system/frontend/src/components/ManagerDashboard.js"], "sourcesContent": ["import React, { useState, useEffect } from 'react';\nimport axios from 'axios';\nimport io from 'socket.io-client';\n\nconst API_BASE_URL = process.env.REACT_APP_API_BASE_URL;\nconst SOCKET_URL = process.env.REACT_APP_SOCKET_URL;\n\nconst ManagerDashboard = ({ user, onLogout }) => {\n  const [activeTab, setActiveTab] = useState('orders');\n  const [orders, setOrders] = useState([]);\n  const [tables, setTables] = useState([]);\n  const [menuItems, setMenuItems] = useState([]);\n  const [cashPendingOrders, setCashPendingOrders] = useState([]);\n  const [socket, setSocket] = useState(null);\n  const [loading, setLoading] = useState(true);\n\n  // Menu management states\n  const [editingItem, setEditingItem] = useState(null);\n  const [newItem, setNewItem] = useState({\n    name: '',\n    description: '',\n    price: '',\n    category: 'mains',\n    available: true\n  });\n  const [showAddForm, setShowAddForm] = useState(false);\n\n  useEffect(() => {\n    fetchOrders();\n    fetchTables();\n    fetchMenuItems();\n    fetchCashPendingOrders();\n    \n    const newSocket = io(SOCKET_URL);\n    setSocket(newSocket);\n\n    newSocket.on('newOrder', (order) => {\n      setOrders(prev => [order, ...prev]);\n    });\n\n    newSocket.on('orderStatusUpdate', (updatedOrder) => {\n      setOrders(prev => prev.map(order =>\n        order._id === updatedOrder._id ? updatedOrder : order\n      ));\n    });\n\n    newSocket.on('tableStatusUpdate', (updatedTable) => {\n      setTables(prev => prev.map(table =>\n        table._id === updatedTable._id ? updatedTable : table\n      ));\n    });\n\n    newSocket.on('cashPaymentPending', (order) => {\n      setCashPendingOrders(prev => [order, ...prev]);\n    });\n\n    newSocket.on('cashPaymentConfirmed', (order) => {\n      setCashPendingOrders(prev => prev.filter(o => o._id !== order._id));\n    });\n\n    return () => newSocket.close();\n  }, []);\n\n  const getAuthHeaders = () => {\n    const token = localStorage.getItem('token');\n    return {\n      headers: {\n        'Authorization': `Bearer ${token}`\n      }\n    };\n  };\n\n  const fetchOrders = async () => {\n    try {\n      const response = await axios.get(`${API_BASE_URL}/api/manager/orders`, getAuthHeaders());\n      setOrders(response.data);\n      setLoading(false);\n    } catch (error) {\n      console.error('Error fetching orders:', error);\n      if (error.response?.status === 401) {\n        onLogout();\n      }\n      setLoading(false);\n    }\n  };\n\n  const fetchTables = async () => {\n    try {\n      const response = await axios.get(`${API_BASE_URL}/api/manager/tables`, getAuthHeaders());\n      setTables(response.data);\n    } catch (error) {\n      console.error('Error fetching tables:', error);\n    }\n  };\n\n  const fetchMenuItems = async () => {\n    try {\n      const response = await axios.get(`${API_BASE_URL}/api/manager/menu`, getAuthHeaders());\n      setMenuItems(response.data);\n    } catch (error) {\n      console.error('Error fetching menu items:', error);\n      if (error.response?.status === 401) {\n        onLogout();\n      }\n    }\n  };\n\n  const fetchCashPendingOrders = async () => {\n    try {\n      const response = await axios.get(`${API_BASE_URL}/api/manager/orders/cash-pending`, getAuthHeaders());\n      setCashPendingOrders(response.data);\n    } catch (error) {\n      console.error('Error fetching cash pending orders:', error);\n    }\n  };\n\n  const updateOrderStatus = async (orderId, status) => {\n    try {\n      await axios.patch(\n        `${API_BASE_URL}/api/manager/orders/${orderId}/status`, \n        { status },\n        getAuthHeaders()\n      );\n    } catch (error) {\n      console.error('Error updating order status:', error);\n      alert('Error updating order status. Please try again.');\n    }\n  };\n\n  const updateTableStatus = async (tableId, status) => {\n    try {\n      await axios.patch(\n        `${API_BASE_URL}/api/manager/tables/${tableId}/status`, \n        { status },\n        getAuthHeaders()\n      );\n      fetchTables();\n    } catch (error) {\n      console.error('Error updating table status:', error);\n    }\n  };\n\n  const confirmCashPayment = async (orderId) => {\n    try {\n      await axios.post(\n        `${API_BASE_URL}/api/orders/${orderId}/payment/cash/confirm`,\n        {},\n        getAuthHeaders()\n      );\n      fetchCashPendingOrders();\n      fetchOrders();\n      fetchTables();\n    } catch (error) {\n      console.error('Error confirming cash payment:', error);\n      alert('Error confirming cash payment. Please try again.');\n    }\n  };\n\n  const renderOrders = () => (\n    <div className=\"orders-section\">\n      <div className=\"section-header\">\n        <h2>📋 Orders ({orders.length})</h2>\n        <p style={{color: '#666', fontSize: '0.9rem'}}>You can only mark ready orders as served</p>\n      </div>\n\n      {orders.map(order => (\n        <div key={order._id} className=\"order-card\">\n          <div className=\"order-header\">\n            <div className=\"order-info\">\n              <h3>Order #{order.orderNumber}</h3>\n              <div className=\"order-meta\">\n                <span className=\"table-info\">📍 Table {order.table.tableNumber}</span>\n                <span className=\"order-time\">🕒 {new Date(order.createdAt).toLocaleTimeString()}</span>\n                <span className=\"customer-info\">👤 {order.customerName}</span>\n              </div>\n            </div>\n            <div className=\"order-status\">\n              <span className={`status ${order.status}`}>\n                {order.status.toUpperCase()}\n              </span>\n            </div>\n          </div>\n\n          <div className=\"order-items\">\n            {order.items.map((item, index) => (\n              <div key={index} className=\"order-item\">\n                <span>{item.quantity}x {item.menuItem.name}</span>\n                <span>${(item.price * item.quantity).toFixed(2)}</span>\n              </div>\n            ))}\n          </div>\n\n          <div className=\"order-total\">\n            Total: ${order.totalAmount.toFixed(2)}\n          </div>\n\n          {order.specialInstructions && order.specialInstructions.trim() !== '' ? (\n            <div style={{\n              marginTop: '10px',\n              padding: '12px',\n              background: '#fff3cd',\n              borderRadius: '8px',\n              border: '2px solid #ffc107'\n            }}>\n              <strong>📝 Special Instructions:</strong> {order.specialInstructions}\n            </div>\n          ) : (\n            <div style={{\n              marginTop: '10px',\n              padding: '8px',\n              background: '#f8f9fa',\n              borderRadius: '6px',\n              color: '#6c757d',\n              fontSize: '0.9rem',\n              fontStyle: 'italic'\n            }}>\n              📝 No special instructions\n            </div>\n          )}\n\n          <div className=\"status-buttons\">\n            {order.status === 'ready' && (\n              <button\n                className=\"btn-serve\"\n                onClick={() => updateOrderStatus(order._id, 'served')}\n              >\n                🍽️ Mark as Served\n              </button>\n            )}\n            {order.status !== 'ready' && order.status !== 'served' && (\n              <div className=\"status-info\">\n                ⏳ Waiting for kitchen to mark as ready\n              </div>\n            )}\n          </div>\n        </div>\n      ))}\n    </div>\n  );\n\n  const renderCashPayments = () => (\n    <div className=\"cash-payments-section\">\n      <div className=\"section-header\">\n        <h2>💰 Cash Payment Confirmations ({cashPendingOrders.length})</h2>\n        {cashPendingOrders.length === 0 && (\n          <p style={{color: '#666', fontStyle: 'italic'}}>No pending cash payments</p>\n        )}\n      </div>\n\n      {cashPendingOrders.map(order => (\n        <div key={order._id} className=\"cash-payment-card\">\n          <div className=\"order-header\">\n            <div className=\"order-info\">\n              <h3>Order #{order.orderNumber}</h3>\n              <div className=\"order-meta\">\n                <span className=\"table-info\">📍 Table {order.table.tableNumber}</span>\n                <span className=\"order-time\">🕒 {new Date(order.createdAt).toLocaleTimeString()}</span>\n                <span className=\"payment-method\">💰 Cash Payment</span>\n              </div>\n            </div>\n            <div className=\"order-total\">\n              <strong>${order.totalAmount.toFixed(2)}</strong>\n            </div>\n          </div>\n\n          <div className=\"customer-details\">\n            <p><strong>Customer:</strong> {order.customerName}</p>\n            <p><strong>Phone:</strong> {order.customerPhone}</p>\n            {order.specialInstructions && order.specialInstructions.trim() !== '' ? (\n              <div style={{\n                marginTop: '10px',\n                padding: '12px',\n                background: '#fff3cd',\n                borderRadius: '8px',\n                border: '2px solid #ffc107'\n              }}>\n                <strong>📝 Special Instructions:</strong> {order.specialInstructions}\n              </div>\n            ) : (\n              <div style={{\n                marginTop: '10px',\n                padding: '8px',\n                background: '#f8f9fa',\n                borderRadius: '6px',\n                color: '#6c757d',\n                fontSize: '0.9rem',\n                fontStyle: 'italic'\n              }}>\n                📝 No special instructions\n              </div>\n            )}\n          </div>\n\n          <div className=\"order-items\">\n            <h4>Items:</h4>\n            {order.items.map((item, index) => (\n              <div key={index} className=\"order-item\">\n                <span>{item.quantity}x {item.menuItem.name}</span>\n                <span>${(item.price * item.quantity).toFixed(2)}</span>\n              </div>\n            ))}\n          </div>\n\n          <div className=\"cash-payment-actions\">\n            <button\n              className=\"btn-confirm-cash\"\n              onClick={() => confirmCashPayment(order._id)}\n            >\n              ✅ Confirm Cash Payment\n            </button>\n          </div>\n        </div>\n      ))}\n    </div>\n  );\n\n  const renderTables = () => (\n    <div className=\"tables-section\">\n      <div className=\"section-header\">\n        <h2>🪑 Table Management ({tables.length} tables)</h2>\n      </div>\n\n      <div className=\"tables-grid\">\n        {tables.map(table => (\n          <div key={table._id} className={`table-card ${table.status}`}>\n            <div className=\"table-header\">\n              <h3>Table {table.tableNumber}</h3>\n              <span className={`table-status ${table.status}`}>\n                {table.status === 'available' && '✅ Available'}\n                {table.status === 'occupied' && '🔴 Occupied'}\n                {table.status === 'reserved' && '🟡 Reserved'}\n                {table.status === 'maintenance' && '🔧 Maintenance'}\n              </span>\n            </div>\n\n            <div className=\"table-info\">\n              <p><strong>Capacity:</strong> {table.capacity} people</p>\n              <p><strong>QR Code:</strong> Generated</p>\n            </div>\n\n            <div className=\"table-actions\">\n              <select\n                value={table.status}\n                onChange={(e) => updateTableStatus(table._id, e.target.value)}\n                className=\"status-select\"\n              >\n                <option value=\"available\">Available</option>\n                <option value=\"occupied\">Occupied</option>\n                <option value=\"reserved\">Reserved</option>\n                <option value=\"maintenance\">Maintenance</option>\n              </select>\n            </div>\n          </div>\n        ))}\n      </div>\n    </div>\n  );\n\n  const handleAddItem = async (e) => {\n    e.preventDefault();\n    try {\n      await axios.post(\n        `${API_BASE_URL}/api/manager/menu`,\n        { ...newItem, price: parseFloat(newItem.price) },\n        getAuthHeaders()\n      );\n      setNewItem({ name: '', description: '', price: '', category: 'mains', available: true });\n      setShowAddForm(false);\n      fetchMenuItems();\n    } catch (error) {\n      console.error('Error adding menu item:', error);\n      alert('Error adding menu item. Please try again.');\n    }\n  };\n\n  const handleUpdateItem = async (itemId, updatedData) => {\n    try {\n      await axios.put(\n        `${API_BASE_URL}/api/manager/menu/${itemId}`,\n        updatedData,\n        getAuthHeaders()\n      );\n      setEditingItem(null);\n      fetchMenuItems();\n    } catch (error) {\n      console.error('Error updating menu item:', error);\n      alert('Error updating menu item. Please try again.');\n    }\n  };\n\n  const handleDeleteItem = async (itemId) => {\n    if (window.confirm('Are you sure you want to delete this menu item?')) {\n      try {\n        await axios.delete(\n          `${API_BASE_URL}/api/manager/menu/${itemId}`,\n          getAuthHeaders()\n        );\n        fetchMenuItems();\n      } catch (error) {\n        console.error('Error deleting menu item:', error);\n        alert('Error deleting menu item. Please try again.');\n      }\n    }\n  };\n\n  const renderMenuManagement = () => {\n    const groupedItems = menuItems.reduce((acc, item) => {\n      if (!acc[item.category]) acc[item.category] = [];\n      acc[item.category].push(item);\n      return acc;\n    }, {});\n\n    return (\n      <div className=\"menu-management-section\">\n        <div className=\"section-header\">\n          <h2>📋 Menu Management ({menuItems.length} items)</h2>\n          <button\n            className=\"btn-add-item\"\n            onClick={() => setShowAddForm(!showAddForm)}\n          >\n            {showAddForm ? '❌ Cancel' : '➕ Add New Item'}\n          </button>\n        </div>\n\n        {showAddForm && (\n          <div className=\"add-item-form\">\n            <h3>Add New Menu Item</h3>\n            <form onSubmit={handleAddItem}>\n              <div className=\"form-row\">\n                <div className=\"form-group\">\n                  <label>Name *</label>\n                  <input\n                    type=\"text\"\n                    value={newItem.name}\n                    onChange={(e) => setNewItem({...newItem, name: e.target.value})}\n                    required\n                  />\n                </div>\n                <div className=\"form-group\">\n                  <label>Price *</label>\n                  <input\n                    type=\"number\"\n                    step=\"0.01\"\n                    value={newItem.price}\n                    onChange={(e) => setNewItem({...newItem, price: e.target.value})}\n                    required\n                  />\n                </div>\n              </div>\n              <div className=\"form-group\">\n                <label>Category *</label>\n                <select\n                  value={newItem.category}\n                  onChange={(e) => setNewItem({...newItem, category: e.target.value})}\n                >\n                  <option value=\"appetizers\">Appetizers</option>\n                  <option value=\"mains\">Main Courses</option>\n                  <option value=\"desserts\">Desserts</option>\n                  <option value=\"beverages\">Beverages</option>\n                </select>\n              </div>\n              <div className=\"form-group\">\n                <label>Description</label>\n                <textarea\n                  value={newItem.description}\n                  onChange={(e) => setNewItem({...newItem, description: e.target.value})}\n                  rows=\"3\"\n                />\n              </div>\n              <div className=\"form-actions\">\n                <button type=\"submit\" className=\"btn-save\">Save Item</button>\n                <button type=\"button\" onClick={() => setShowAddForm(false)} className=\"btn-cancel\">Cancel</button>\n              </div>\n            </form>\n          </div>\n        )}\n\n        {Object.keys(groupedItems).map(category => (\n          <div key={category} className=\"menu-category-section\">\n            <h3 className=\"category-title\">\n              {category === 'appetizers' && '🥗'}\n              {category === 'mains' && '🍽️'}\n              {category === 'desserts' && '🍰'}\n              {category === 'beverages' && '🥤'}\n              {category.charAt(0).toUpperCase() + category.slice(1)} ({groupedItems[category].length})\n            </h3>\n\n            <div className=\"menu-items-grid\">\n              {groupedItems[category].map(item => (\n                <div key={item._id} className=\"menu-item-card\">\n                  {editingItem === item._id ? (\n                    <EditItemForm\n                      item={item}\n                      onSave={(data) => handleUpdateItem(item._id, data)}\n                      onCancel={() => setEditingItem(null)}\n                    />\n                  ) : (\n                    <>\n                      <div className=\"item-header\">\n                        <h4>{item.name}</h4>\n                        <span className=\"item-price\">${item.price.toFixed(2)}</span>\n                      </div>\n                      <p className=\"item-description\">{item.description}</p>\n                      <div className=\"item-status\">\n                        <span className={`availability ${item.available ? 'available' : 'unavailable'}`}>\n                          {item.available ? '✅ Available' : '❌ Unavailable'}\n                        </span>\n                      </div>\n                      <div className=\"item-actions\">\n                        <button\n                          className=\"btn-edit\"\n                          onClick={() => setEditingItem(item._id)}\n                        >\n                          ✏️ Edit\n                        </button>\n                        <button\n                          className=\"btn-delete\"\n                          onClick={() => handleDeleteItem(item._id)}\n                        >\n                          🗑️ Delete\n                        </button>\n                      </div>\n                    </>\n                  )}\n                </div>\n              ))}\n            </div>\n          </div>\n        ))}\n      </div>\n    );\n  };\n\n  // EditItemForm component - moved outside to avoid hooks issues\n  const EditItemForm = ({ item, onSave, onCancel }) => {\n    // Use a simple form without useState to avoid hooks issues\n    const handleSubmit = (e) => {\n      e.preventDefault();\n      const formData = new FormData(e.target);\n      const updatedData = {\n        name: formData.get('name'),\n        description: formData.get('description'),\n        price: parseFloat(formData.get('price')),\n        category: formData.get('category'),\n        available: formData.get('available') === 'on'\n      };\n      onSave(updatedData);\n    };\n\n    return (\n      <form onSubmit={handleSubmit} className=\"edit-item-form\">\n        <div className=\"form-group\">\n          <input\n            type=\"text\"\n            name=\"name\"\n            defaultValue={item.name}\n            required\n            placeholder=\"Item name\"\n          />\n        </div>\n        <div className=\"form-group\">\n          <input\n            type=\"number\"\n            name=\"price\"\n            step=\"0.01\"\n            defaultValue={item.price}\n            required\n            placeholder=\"Price\"\n          />\n        </div>\n        <div className=\"form-group\">\n          <select name=\"category\" defaultValue={item.category}>\n            <option value=\"appetizers\">Appetizers</option>\n            <option value=\"mains\">Main Courses</option>\n            <option value=\"desserts\">Desserts</option>\n            <option value=\"beverages\">Beverages</option>\n          </select>\n        </div>\n        <div className=\"form-group\">\n          <textarea\n            name=\"description\"\n            defaultValue={item.description}\n            rows=\"2\"\n            placeholder=\"Description\"\n          />\n        </div>\n        <div className=\"form-group\">\n          <label>\n            <input\n              type=\"checkbox\"\n              name=\"available\"\n              defaultChecked={item.available}\n            />\n            Available\n          </label>\n        </div>\n        <div className=\"form-actions\">\n          <button type=\"submit\" className=\"btn-save\">Save</button>\n          <button type=\"button\" onClick={onCancel} className=\"btn-cancel\">Cancel</button>\n        </div>\n      </form>\n    );\n  };\n\n  if (loading) {\n    return (\n      <div className=\"manager-dashboard\">\n        <div className=\"loading\">\n          <div className=\"spinner\"></div>\n          <p>Loading manager dashboard...</p>\n        </div>\n      </div>\n    );\n  }\n\n  return (\n    <div className=\"manager-dashboard\">\n      <div className=\"manager-header\">\n        <div className=\"header-left\">\n          <h1>👨‍💼 Manager Dashboard</h1>\n          <p>Welcome, {user.name}</p>\n        </div>\n        <div className=\"header-right\">\n          <button className=\"logout-btn\" onClick={onLogout}>\n            🚪 Logout\n          </button>\n        </div>\n      </div>\n      \n      <div className=\"admin-tabs\">\n        <button \n          className={activeTab === 'orders' ? 'active' : ''}\n          onClick={() => setActiveTab('orders')}\n        >\n          Orders\n        </button>\n        <button \n          className={activeTab === 'cash-payments' ? 'active' : ''}\n          onClick={() => setActiveTab('cash-payments')}\n        >\n          Cash Payments {cashPendingOrders.length > 0 && <span className=\"notification-badge\">{cashPendingOrders.length}</span>}\n        </button>\n        <button \n          className={activeTab === 'tables' ? 'active' : ''}\n          onClick={() => setActiveTab('tables')}\n        >\n          Tables\n        </button>\n        <button \n          className={activeTab === 'menu' ? 'active' : ''}\n          onClick={() => setActiveTab('menu')}\n        >\n          Menu Management\n        </button>\n      </div>\n\n      <div className=\"tab-content\">\n        {activeTab === 'orders' && renderOrders()}\n        {activeTab === 'cash-payments' && renderCashPayments()}\n        {activeTab === 'tables' && renderTables()}\n        {activeTab === 'menu' && renderMenuManagement()}\n      </div>\n    </div>\n  );\n};\n\nexport default ManagerDashboard;\n"], "mappings": ";;AAAA,OAAOA,KAAK,IAAIC,QAAQ,EAAEC,SAAS,QAAQ,OAAO;AAClD,OAAOC,KAAK,MAAM,OAAO;AACzB,OAAOC,EAAE,MAAM,kBAAkB;AAAC,SAAAC,MAAA,IAAAC,OAAA,EAAAC,QAAA,IAAAC,SAAA;AAElC,MAAMC,YAAY,GAAGC,OAAO,CAACC,GAAG,CAACC,sBAAsB;AACvD,MAAMC,UAAU,GAAGH,OAAO,CAACC,GAAG,CAACG,oBAAoB;AAEnD,MAAMC,gBAAgB,GAAGA,CAAC;EAAEC,IAAI;EAAEC;AAAS,CAAC,KAAK;EAAAC,EAAA;EAC/C,MAAM,CAACC,SAAS,EAAEC,YAAY,CAAC,GAAGnB,QAAQ,CAAC,QAAQ,CAAC;EACpD,MAAM,CAACoB,MAAM,EAAEC,SAAS,CAAC,GAAGrB,QAAQ,CAAC,EAAE,CAAC;EACxC,MAAM,CAACsB,MAAM,EAAEC,SAAS,CAAC,GAAGvB,QAAQ,CAAC,EAAE,CAAC;EACxC,MAAM,CAACwB,SAAS,EAAEC,YAAY,CAAC,GAAGzB,QAAQ,CAAC,EAAE,CAAC;EAC9C,MAAM,CAAC0B,iBAAiB,EAAEC,oBAAoB,CAAC,GAAG3B,QAAQ,CAAC,EAAE,CAAC;EAC9D,MAAM,CAAC4B,MAAM,EAAEC,SAAS,CAAC,GAAG7B,QAAQ,CAAC,IAAI,CAAC;EAC1C,MAAM,CAAC8B,OAAO,EAAEC,UAAU,CAAC,GAAG/B,QAAQ,CAAC,IAAI,CAAC;;EAE5C;EACA,MAAM,CAACgC,WAAW,EAAEC,cAAc,CAAC,GAAGjC,QAAQ,CAAC,IAAI,CAAC;EACpD,MAAM,CAACkC,OAAO,EAAEC,UAAU,CAAC,GAAGnC,QAAQ,CAAC;IACrCoC,IAAI,EAAE,EAAE;IACRC,WAAW,EAAE,EAAE;IACfC,KAAK,EAAE,EAAE;IACTC,QAAQ,EAAE,OAAO;IACjBC,SAAS,EAAE;EACb,CAAC,CAAC;EACF,MAAM,CAACC,WAAW,EAAEC,cAAc,CAAC,GAAG1C,QAAQ,CAAC,KAAK,CAAC;EAErDC,SAAS,CAAC,MAAM;IACd0C,WAAW,CAAC,CAAC;IACbC,WAAW,CAAC,CAAC;IACbC,cAAc,CAAC,CAAC;IAChBC,sBAAsB,CAAC,CAAC;IAExB,MAAMC,SAAS,GAAG5C,EAAE,CAACS,UAAU,CAAC;IAChCiB,SAAS,CAACkB,SAAS,CAAC;IAEpBA,SAAS,CAACC,EAAE,CAAC,UAAU,EAAGC,KAAK,IAAK;MAClC5B,SAAS,CAAC6B,IAAI,IAAI,CAACD,KAAK,EAAE,GAAGC,IAAI,CAAC,CAAC;IACrC,CAAC,CAAC;IAEFH,SAAS,CAACC,EAAE,CAAC,mBAAmB,EAAGG,YAAY,IAAK;MAClD9B,SAAS,CAAC6B,IAAI,IAAIA,IAAI,CAACE,GAAG,CAACH,KAAK,IAC9BA,KAAK,CAACI,GAAG,KAAKF,YAAY,CAACE,GAAG,GAAGF,YAAY,GAAGF,KAClD,CAAC,CAAC;IACJ,CAAC,CAAC;IAEFF,SAAS,CAACC,EAAE,CAAC,mBAAmB,EAAGM,YAAY,IAAK;MAClD/B,SAAS,CAAC2B,IAAI,IAAIA,IAAI,CAACE,GAAG,CAACG,KAAK,IAC9BA,KAAK,CAACF,GAAG,KAAKC,YAAY,CAACD,GAAG,GAAGC,YAAY,GAAGC,KAClD,CAAC,CAAC;IACJ,CAAC,CAAC;IAEFR,SAAS,CAACC,EAAE,CAAC,oBAAoB,EAAGC,KAAK,IAAK;MAC5CtB,oBAAoB,CAACuB,IAAI,IAAI,CAACD,KAAK,EAAE,GAAGC,IAAI,CAAC,CAAC;IAChD,CAAC,CAAC;IAEFH,SAAS,CAACC,EAAE,CAAC,sBAAsB,EAAGC,KAAK,IAAK;MAC9CtB,oBAAoB,CAACuB,IAAI,IAAIA,IAAI,CAACM,MAAM,CAACC,CAAC,IAAIA,CAAC,CAACJ,GAAG,KAAKJ,KAAK,CAACI,GAAG,CAAC,CAAC;IACrE,CAAC,CAAC;IAEF,OAAO,MAAMN,SAAS,CAACW,KAAK,CAAC,CAAC;EAChC,CAAC,EAAE,EAAE,CAAC;EAEN,MAAMC,cAAc,GAAGA,CAAA,KAAM;IAC3B,MAAMC,KAAK,GAAGC,YAAY,CAACC,OAAO,CAAC,OAAO,CAAC;IAC3C,OAAO;MACLC,OAAO,EAAE;QACP,eAAe,EAAE,UAAUH,KAAK;MAClC;IACF,CAAC;EACH,CAAC;EAED,MAAMjB,WAAW,GAAG,MAAAA,CAAA,KAAY;IAC9B,IAAI;MACF,MAAMqB,QAAQ,GAAG,MAAM9D,KAAK,CAAC+D,GAAG,CAAC,GAAGzD,YAAY,qBAAqB,EAAEmD,cAAc,CAAC,CAAC,CAAC;MACxFtC,SAAS,CAAC2C,QAAQ,CAACE,IAAI,CAAC;MACxBnC,UAAU,CAAC,KAAK,CAAC;IACnB,CAAC,CAAC,OAAOoC,KAAK,EAAE;MAAA,IAAAC,eAAA;MACdC,OAAO,CAACF,KAAK,CAAC,wBAAwB,EAAEA,KAAK,CAAC;MAC9C,IAAI,EAAAC,eAAA,GAAAD,KAAK,CAACH,QAAQ,cAAAI,eAAA,uBAAdA,eAAA,CAAgBE,MAAM,MAAK,GAAG,EAAE;QAClCtD,QAAQ,CAAC,CAAC;MACZ;MACAe,UAAU,CAAC,KAAK,CAAC;IACnB;EACF,CAAC;EAED,MAAMa,WAAW,GAAG,MAAAA,CAAA,KAAY;IAC9B,IAAI;MACF,MAAMoB,QAAQ,GAAG,MAAM9D,KAAK,CAAC+D,GAAG,CAAC,GAAGzD,YAAY,qBAAqB,EAAEmD,cAAc,CAAC,CAAC,CAAC;MACxFpC,SAAS,CAACyC,QAAQ,CAACE,IAAI,CAAC;IAC1B,CAAC,CAAC,OAAOC,KAAK,EAAE;MACdE,OAAO,CAACF,KAAK,CAAC,wBAAwB,EAAEA,KAAK,CAAC;IAChD;EACF,CAAC;EAED,MAAMtB,cAAc,GAAG,MAAAA,CAAA,KAAY;IACjC,IAAI;MACF,MAAMmB,QAAQ,GAAG,MAAM9D,KAAK,CAAC+D,GAAG,CAAC,GAAGzD,YAAY,mBAAmB,EAAEmD,cAAc,CAAC,CAAC,CAAC;MACtFlC,YAAY,CAACuC,QAAQ,CAACE,IAAI,CAAC;IAC7B,CAAC,CAAC,OAAOC,KAAK,EAAE;MAAA,IAAAI,gBAAA;MACdF,OAAO,CAACF,KAAK,CAAC,4BAA4B,EAAEA,KAAK,CAAC;MAClD,IAAI,EAAAI,gBAAA,GAAAJ,KAAK,CAACH,QAAQ,cAAAO,gBAAA,uBAAdA,gBAAA,CAAgBD,MAAM,MAAK,GAAG,EAAE;QAClCtD,QAAQ,CAAC,CAAC;MACZ;IACF;EACF,CAAC;EAED,MAAM8B,sBAAsB,GAAG,MAAAA,CAAA,KAAY;IACzC,IAAI;MACF,MAAMkB,QAAQ,GAAG,MAAM9D,KAAK,CAAC+D,GAAG,CAAC,GAAGzD,YAAY,kCAAkC,EAAEmD,cAAc,CAAC,CAAC,CAAC;MACrGhC,oBAAoB,CAACqC,QAAQ,CAACE,IAAI,CAAC;IACrC,CAAC,CAAC,OAAOC,KAAK,EAAE;MACdE,OAAO,CAACF,KAAK,CAAC,qCAAqC,EAAEA,KAAK,CAAC;IAC7D;EACF,CAAC;EAED,MAAMK,iBAAiB,GAAG,MAAAA,CAAOC,OAAO,EAAEH,MAAM,KAAK;IACnD,IAAI;MACF,MAAMpE,KAAK,CAACwE,KAAK,CACf,GAAGlE,YAAY,uBAAuBiE,OAAO,SAAS,EACtD;QAAEH;MAAO,CAAC,EACVX,cAAc,CAAC,CACjB,CAAC;IACH,CAAC,CAAC,OAAOQ,KAAK,EAAE;MACdE,OAAO,CAACF,KAAK,CAAC,8BAA8B,EAAEA,KAAK,CAAC;MACpDQ,KAAK,CAAC,gDAAgD,CAAC;IACzD;EACF,CAAC;EAED,MAAMC,iBAAiB,GAAG,MAAAA,CAAOC,OAAO,EAAEP,MAAM,KAAK;IACnD,IAAI;MACF,MAAMpE,KAAK,CAACwE,KAAK,CACf,GAAGlE,YAAY,uBAAuBqE,OAAO,SAAS,EACtD;QAAEP;MAAO,CAAC,EACVX,cAAc,CAAC,CACjB,CAAC;MACDf,WAAW,CAAC,CAAC;IACf,CAAC,CAAC,OAAOuB,KAAK,EAAE;MACdE,OAAO,CAACF,KAAK,CAAC,8BAA8B,EAAEA,KAAK,CAAC;IACtD;EACF,CAAC;EAED,MAAMW,kBAAkB,GAAG,MAAOL,OAAO,IAAK;IAC5C,IAAI;MACF,MAAMvE,KAAK,CAAC6E,IAAI,CACd,GAAGvE,YAAY,eAAeiE,OAAO,uBAAuB,EAC5D,CAAC,CAAC,EACFd,cAAc,CAAC,CACjB,CAAC;MACDb,sBAAsB,CAAC,CAAC;MACxBH,WAAW,CAAC,CAAC;MACbC,WAAW,CAAC,CAAC;IACf,CAAC,CAAC,OAAOuB,KAAK,EAAE;MACdE,OAAO,CAACF,KAAK,CAAC,gCAAgC,EAAEA,KAAK,CAAC;MACtDQ,KAAK,CAAC,kDAAkD,CAAC;IAC3D;EACF,CAAC;EAED,MAAMK,YAAY,GAAGA,CAAA,kBACnB3E,OAAA;IAAK4E,SAAS,EAAC,gBAAgB;IAAAC,QAAA,gBAC7B7E,OAAA;MAAK4E,SAAS,EAAC,gBAAgB;MAAAC,QAAA,gBAC7B7E,OAAA;QAAA6E,QAAA,GAAI,uBAAW,EAAC9D,MAAM,CAAC+D,MAAM,EAAC,GAAC;MAAA;QAAAC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAI,CAAC,eACpClF,OAAA;QAAGmF,KAAK,EAAE;UAACC,KAAK,EAAE,MAAM;UAAEC,QAAQ,EAAE;QAAQ,CAAE;QAAAR,QAAA,EAAC;MAAwC;QAAAE,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAG,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACxF,CAAC,EAELnE,MAAM,CAACgC,GAAG,CAACH,KAAK,iBACf5C,OAAA;MAAqB4E,SAAS,EAAC,YAAY;MAAAC,QAAA,gBACzC7E,OAAA;QAAK4E,SAAS,EAAC,cAAc;QAAAC,QAAA,gBAC3B7E,OAAA;UAAK4E,SAAS,EAAC,YAAY;UAAAC,QAAA,gBACzB7E,OAAA;YAAA6E,QAAA,GAAI,SAAO,EAACjC,KAAK,CAAC0C,WAAW;UAAA;YAAAP,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAK,CAAC,eACnClF,OAAA;YAAK4E,SAAS,EAAC,YAAY;YAAAC,QAAA,gBACzB7E,OAAA;cAAM4E,SAAS,EAAC,YAAY;cAAAC,QAAA,GAAC,qBAAS,EAACjC,KAAK,CAACM,KAAK,CAACqC,WAAW;YAAA;cAAAR,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAO,CAAC,eACtElF,OAAA;cAAM4E,SAAS,EAAC,YAAY;cAAAC,QAAA,GAAC,eAAG,EAAC,IAAIW,IAAI,CAAC5C,KAAK,CAAC6C,SAAS,CAAC,CAACC,kBAAkB,CAAC,CAAC;YAAA;cAAAX,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAO,CAAC,eACvFlF,OAAA;cAAM4E,SAAS,EAAC,eAAe;cAAAC,QAAA,GAAC,eAAG,EAACjC,KAAK,CAAC+C,YAAY;YAAA;cAAAZ,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAO,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAC3D,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH,CAAC,eACNlF,OAAA;UAAK4E,SAAS,EAAC,cAAc;UAAAC,QAAA,eAC3B7E,OAAA;YAAM4E,SAAS,EAAE,UAAUhC,KAAK,CAACqB,MAAM,EAAG;YAAAY,QAAA,EACvCjC,KAAK,CAACqB,MAAM,CAAC2B,WAAW,CAAC;UAAC;YAAAb,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACvB;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACJ,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH,CAAC,eAENlF,OAAA;QAAK4E,SAAS,EAAC,aAAa;QAAAC,QAAA,EACzBjC,KAAK,CAACiD,KAAK,CAAC9C,GAAG,CAAC,CAAC+C,IAAI,EAAEC,KAAK,kBAC3B/F,OAAA;UAAiB4E,SAAS,EAAC,YAAY;UAAAC,QAAA,gBACrC7E,OAAA;YAAA6E,QAAA,GAAOiB,IAAI,CAACE,QAAQ,EAAC,IAAE,EAACF,IAAI,CAACG,QAAQ,CAAClE,IAAI;UAAA;YAAAgD,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAO,CAAC,eAClDlF,OAAA;YAAA6E,QAAA,GAAM,GAAC,EAAC,CAACiB,IAAI,CAAC7D,KAAK,GAAG6D,IAAI,CAACE,QAAQ,EAAEE,OAAO,CAAC,CAAC,CAAC;UAAA;YAAAnB,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAO,CAAC;QAAA,GAF/Ca,KAAK;UAAAhB,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAGV,CACN;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACC,CAAC,eAENlF,OAAA;QAAK4E,SAAS,EAAC,aAAa;QAAAC,QAAA,GAAC,UACnB,EAACjC,KAAK,CAACuD,WAAW,CAACD,OAAO,CAAC,CAAC,CAAC;MAAA;QAAAnB,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAClC,CAAC,EAELtC,KAAK,CAACwD,mBAAmB,IAAIxD,KAAK,CAACwD,mBAAmB,CAACC,IAAI,CAAC,CAAC,KAAK,EAAE,gBACnErG,OAAA;QAAKmF,KAAK,EAAE;UACVmB,SAAS,EAAE,MAAM;UACjBC,OAAO,EAAE,MAAM;UACfC,UAAU,EAAE,SAAS;UACrBC,YAAY,EAAE,KAAK;UACnBC,MAAM,EAAE;QACV,CAAE;QAAA7B,QAAA,gBACA7E,OAAA;UAAA6E,QAAA,EAAQ;QAAwB;UAAAE,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAQ,CAAC,KAAC,EAACtC,KAAK,CAACwD,mBAAmB;MAAA;QAAArB,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACjE,CAAC,gBAENlF,OAAA;QAAKmF,KAAK,EAAE;UACVmB,SAAS,EAAE,MAAM;UACjBC,OAAO,EAAE,KAAK;UACdC,UAAU,EAAE,SAAS;UACrBC,YAAY,EAAE,KAAK;UACnBrB,KAAK,EAAE,SAAS;UAChBC,QAAQ,EAAE,QAAQ;UAClBsB,SAAS,EAAE;QACb,CAAE;QAAA9B,QAAA,EAAC;MAEH;QAAAE,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAK,CACN,eAEDlF,OAAA;QAAK4E,SAAS,EAAC,gBAAgB;QAAAC,QAAA,GAC5BjC,KAAK,CAACqB,MAAM,KAAK,OAAO,iBACvBjE,OAAA;UACE4E,SAAS,EAAC,WAAW;UACrBgC,OAAO,EAAEA,CAAA,KAAMzC,iBAAiB,CAACvB,KAAK,CAACI,GAAG,EAAE,QAAQ,CAAE;UAAA6B,QAAA,EACvD;QAED;UAAAE,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAQ,CACT,EACAtC,KAAK,CAACqB,MAAM,KAAK,OAAO,IAAIrB,KAAK,CAACqB,MAAM,KAAK,QAAQ,iBACpDjE,OAAA;UAAK4E,SAAS,EAAC,aAAa;UAAAC,QAAA,EAAC;QAE7B;UAAAE,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAK,CACN;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACE,CAAC;IAAA,GApEEtC,KAAK,CAACI,GAAG;MAAA+B,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAqEd,CACN,CAAC;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACC,CACN;EAED,MAAM2B,kBAAkB,GAAGA,CAAA,kBACzB7G,OAAA;IAAK4E,SAAS,EAAC,uBAAuB;IAAAC,QAAA,gBACpC7E,OAAA;MAAK4E,SAAS,EAAC,gBAAgB;MAAAC,QAAA,gBAC7B7E,OAAA;QAAA6E,QAAA,GAAI,2CAA+B,EAACxD,iBAAiB,CAACyD,MAAM,EAAC,GAAC;MAAA;QAAAC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAI,CAAC,EAClE7D,iBAAiB,CAACyD,MAAM,KAAK,CAAC,iBAC7B9E,OAAA;QAAGmF,KAAK,EAAE;UAACC,KAAK,EAAE,MAAM;UAAEuB,SAAS,EAAE;QAAQ,CAAE;QAAA9B,QAAA,EAAC;MAAwB;QAAAE,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAG,CAC5E;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACE,CAAC,EAEL7D,iBAAiB,CAAC0B,GAAG,CAACH,KAAK,iBAC1B5C,OAAA;MAAqB4E,SAAS,EAAC,mBAAmB;MAAAC,QAAA,gBAChD7E,OAAA;QAAK4E,SAAS,EAAC,cAAc;QAAAC,QAAA,gBAC3B7E,OAAA;UAAK4E,SAAS,EAAC,YAAY;UAAAC,QAAA,gBACzB7E,OAAA;YAAA6E,QAAA,GAAI,SAAO,EAACjC,KAAK,CAAC0C,WAAW;UAAA;YAAAP,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAK,CAAC,eACnClF,OAAA;YAAK4E,SAAS,EAAC,YAAY;YAAAC,QAAA,gBACzB7E,OAAA;cAAM4E,SAAS,EAAC,YAAY;cAAAC,QAAA,GAAC,qBAAS,EAACjC,KAAK,CAACM,KAAK,CAACqC,WAAW;YAAA;cAAAR,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAO,CAAC,eACtElF,OAAA;cAAM4E,SAAS,EAAC,YAAY;cAAAC,QAAA,GAAC,eAAG,EAAC,IAAIW,IAAI,CAAC5C,KAAK,CAAC6C,SAAS,CAAC,CAACC,kBAAkB,CAAC,CAAC;YAAA;cAAAX,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAO,CAAC,eACvFlF,OAAA;cAAM4E,SAAS,EAAC,gBAAgB;cAAAC,QAAA,EAAC;YAAe;cAAAE,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAM,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACpD,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH,CAAC,eACNlF,OAAA;UAAK4E,SAAS,EAAC,aAAa;UAAAC,QAAA,eAC1B7E,OAAA;YAAA6E,QAAA,GAAQ,GAAC,EAACjC,KAAK,CAACuD,WAAW,CAACD,OAAO,CAAC,CAAC,CAAC;UAAA;YAAAnB,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAS;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAC7C,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH,CAAC,eAENlF,OAAA;QAAK4E,SAAS,EAAC,kBAAkB;QAAAC,QAAA,gBAC/B7E,OAAA;UAAA6E,QAAA,gBAAG7E,OAAA;YAAA6E,QAAA,EAAQ;UAAS;YAAAE,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAQ,CAAC,KAAC,EAACtC,KAAK,CAAC+C,YAAY;QAAA;UAAAZ,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAI,CAAC,eACtDlF,OAAA;UAAA6E,QAAA,gBAAG7E,OAAA;YAAA6E,QAAA,EAAQ;UAAM;YAAAE,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAQ,CAAC,KAAC,EAACtC,KAAK,CAACkE,aAAa;QAAA;UAAA/B,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAI,CAAC,EACnDtC,KAAK,CAACwD,mBAAmB,IAAIxD,KAAK,CAACwD,mBAAmB,CAACC,IAAI,CAAC,CAAC,KAAK,EAAE,gBACnErG,OAAA;UAAKmF,KAAK,EAAE;YACVmB,SAAS,EAAE,MAAM;YACjBC,OAAO,EAAE,MAAM;YACfC,UAAU,EAAE,SAAS;YACrBC,YAAY,EAAE,KAAK;YACnBC,MAAM,EAAE;UACV,CAAE;UAAA7B,QAAA,gBACA7E,OAAA;YAAA6E,QAAA,EAAQ;UAAwB;YAAAE,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAQ,CAAC,KAAC,EAACtC,KAAK,CAACwD,mBAAmB;QAAA;UAAArB,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACjE,CAAC,gBAENlF,OAAA;UAAKmF,KAAK,EAAE;YACVmB,SAAS,EAAE,MAAM;YACjBC,OAAO,EAAE,KAAK;YACdC,UAAU,EAAE,SAAS;YACrBC,YAAY,EAAE,KAAK;YACnBrB,KAAK,EAAE,SAAS;YAChBC,QAAQ,EAAE,QAAQ;YAClBsB,SAAS,EAAE;UACb,CAAE;UAAA9B,QAAA,EAAC;QAEH;UAAAE,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAK,CACN;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACE,CAAC,eAENlF,OAAA;QAAK4E,SAAS,EAAC,aAAa;QAAAC,QAAA,gBAC1B7E,OAAA;UAAA6E,QAAA,EAAI;QAAM;UAAAE,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAI,CAAC,EACdtC,KAAK,CAACiD,KAAK,CAAC9C,GAAG,CAAC,CAAC+C,IAAI,EAAEC,KAAK,kBAC3B/F,OAAA;UAAiB4E,SAAS,EAAC,YAAY;UAAAC,QAAA,gBACrC7E,OAAA;YAAA6E,QAAA,GAAOiB,IAAI,CAACE,QAAQ,EAAC,IAAE,EAACF,IAAI,CAACG,QAAQ,CAAClE,IAAI;UAAA;YAAAgD,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAO,CAAC,eAClDlF,OAAA;YAAA6E,QAAA,GAAM,GAAC,EAAC,CAACiB,IAAI,CAAC7D,KAAK,GAAG6D,IAAI,CAACE,QAAQ,EAAEE,OAAO,CAAC,CAAC,CAAC;UAAA;YAAAnB,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAO,CAAC;QAAA,GAF/Ca,KAAK;UAAAhB,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAGV,CACN,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACC,CAAC,eAENlF,OAAA;QAAK4E,SAAS,EAAC,sBAAsB;QAAAC,QAAA,eACnC7E,OAAA;UACE4E,SAAS,EAAC,kBAAkB;UAC5BgC,OAAO,EAAEA,CAAA,KAAMnC,kBAAkB,CAAC7B,KAAK,CAACI,GAAG,CAAE;UAAA6B,QAAA,EAC9C;QAED;UAAAE,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAQ;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACN,CAAC;IAAA,GA5DEtC,KAAK,CAACI,GAAG;MAAA+B,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OA6Dd,CACN,CAAC;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACC,CACN;EAED,MAAM6B,YAAY,GAAGA,CAAA,kBACnB/G,OAAA;IAAK4E,SAAS,EAAC,gBAAgB;IAAAC,QAAA,gBAC7B7E,OAAA;MAAK4E,SAAS,EAAC,gBAAgB;MAAAC,QAAA,eAC7B7E,OAAA;QAAA6E,QAAA,GAAI,iCAAqB,EAAC5D,MAAM,CAAC6D,MAAM,EAAC,UAAQ;MAAA;QAAAC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAI;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAClD,CAAC,eAENlF,OAAA;MAAK4E,SAAS,EAAC,aAAa;MAAAC,QAAA,EACzB5D,MAAM,CAAC8B,GAAG,CAACG,KAAK,iBACflD,OAAA;QAAqB4E,SAAS,EAAE,cAAc1B,KAAK,CAACe,MAAM,EAAG;QAAAY,QAAA,gBAC3D7E,OAAA;UAAK4E,SAAS,EAAC,cAAc;UAAAC,QAAA,gBAC3B7E,OAAA;YAAA6E,QAAA,GAAI,QAAM,EAAC3B,KAAK,CAACqC,WAAW;UAAA;YAAAR,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAK,CAAC,eAClClF,OAAA;YAAM4E,SAAS,EAAE,gBAAgB1B,KAAK,CAACe,MAAM,EAAG;YAAAY,QAAA,GAC7C3B,KAAK,CAACe,MAAM,KAAK,WAAW,IAAI,aAAa,EAC7Cf,KAAK,CAACe,MAAM,KAAK,UAAU,IAAI,aAAa,EAC5Cf,KAAK,CAACe,MAAM,KAAK,UAAU,IAAI,aAAa,EAC5Cf,KAAK,CAACe,MAAM,KAAK,aAAa,IAAI,gBAAgB;UAAA;YAAAc,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAC/C,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACJ,CAAC,eAENlF,OAAA;UAAK4E,SAAS,EAAC,YAAY;UAAAC,QAAA,gBACzB7E,OAAA;YAAA6E,QAAA,gBAAG7E,OAAA;cAAA6E,QAAA,EAAQ;YAAS;cAAAE,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAQ,CAAC,KAAC,EAAChC,KAAK,CAAC8D,QAAQ,EAAC,SAAO;UAAA;YAAAjC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAG,CAAC,eACzDlF,OAAA;YAAA6E,QAAA,gBAAG7E,OAAA;cAAA6E,QAAA,EAAQ;YAAQ;cAAAE,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAQ,CAAC,cAAU;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAG,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACvC,CAAC,eAENlF,OAAA;UAAK4E,SAAS,EAAC,eAAe;UAAAC,QAAA,eAC5B7E,OAAA;YACEiH,KAAK,EAAE/D,KAAK,CAACe,MAAO;YACpBiD,QAAQ,EAAGC,CAAC,IAAK5C,iBAAiB,CAACrB,KAAK,CAACF,GAAG,EAAEmE,CAAC,CAACC,MAAM,CAACH,KAAK,CAAE;YAC9DrC,SAAS,EAAC,eAAe;YAAAC,QAAA,gBAEzB7E,OAAA;cAAQiH,KAAK,EAAC,WAAW;cAAApC,QAAA,EAAC;YAAS;cAAAE,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAQ,CAAC,eAC5ClF,OAAA;cAAQiH,KAAK,EAAC,UAAU;cAAApC,QAAA,EAAC;YAAQ;cAAAE,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAQ,CAAC,eAC1ClF,OAAA;cAAQiH,KAAK,EAAC,UAAU;cAAApC,QAAA,EAAC;YAAQ;cAAAE,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAQ,CAAC,eAC1ClF,OAAA;cAAQiH,KAAK,EAAC,aAAa;cAAApC,QAAA,EAAC;YAAW;cAAAE,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAQ,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAC1C;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACN,CAAC;MAAA,GA3BEhC,KAAK,CAACF,GAAG;QAAA+B,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OA4Bd,CACN;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACC,CAAC;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACH,CACN;EAED,MAAMmC,aAAa,GAAG,MAAOF,CAAC,IAAK;IACjCA,CAAC,CAACG,cAAc,CAAC,CAAC;IAClB,IAAI;MACF,MAAMzH,KAAK,CAAC6E,IAAI,CACd,GAAGvE,YAAY,mBAAmB,EAClC;QAAE,GAAG0B,OAAO;QAAEI,KAAK,EAAEsF,UAAU,CAAC1F,OAAO,CAACI,KAAK;MAAE,CAAC,EAChDqB,cAAc,CAAC,CACjB,CAAC;MACDxB,UAAU,CAAC;QAAEC,IAAI,EAAE,EAAE;QAAEC,WAAW,EAAE,EAAE;QAAEC,KAAK,EAAE,EAAE;QAAEC,QAAQ,EAAE,OAAO;QAAEC,SAAS,EAAE;MAAK,CAAC,CAAC;MACxFE,cAAc,CAAC,KAAK,CAAC;MACrBG,cAAc,CAAC,CAAC;IAClB,CAAC,CAAC,OAAOsB,KAAK,EAAE;MACdE,OAAO,CAACF,KAAK,CAAC,yBAAyB,EAAEA,KAAK,CAAC;MAC/CQ,KAAK,CAAC,2CAA2C,CAAC;IACpD;EACF,CAAC;EAED,MAAMkD,gBAAgB,GAAG,MAAAA,CAAOC,MAAM,EAAEC,WAAW,KAAK;IACtD,IAAI;MACF,MAAM7H,KAAK,CAAC8H,GAAG,CACb,GAAGxH,YAAY,qBAAqBsH,MAAM,EAAE,EAC5CC,WAAW,EACXpE,cAAc,CAAC,CACjB,CAAC;MACD1B,cAAc,CAAC,IAAI,CAAC;MACpBY,cAAc,CAAC,CAAC;IAClB,CAAC,CAAC,OAAOsB,KAAK,EAAE;MACdE,OAAO,CAACF,KAAK,CAAC,2BAA2B,EAAEA,KAAK,CAAC;MACjDQ,KAAK,CAAC,6CAA6C,CAAC;IACtD;EACF,CAAC;EAED,MAAMsD,gBAAgB,GAAG,MAAOH,MAAM,IAAK;IACzC,IAAII,MAAM,CAACC,OAAO,CAAC,iDAAiD,CAAC,EAAE;MACrE,IAAI;QACF,MAAMjI,KAAK,CAACkI,MAAM,CAChB,GAAG5H,YAAY,qBAAqBsH,MAAM,EAAE,EAC5CnE,cAAc,CAAC,CACjB,CAAC;QACDd,cAAc,CAAC,CAAC;MAClB,CAAC,CAAC,OAAOsB,KAAK,EAAE;QACdE,OAAO,CAACF,KAAK,CAAC,2BAA2B,EAAEA,KAAK,CAAC;QACjDQ,KAAK,CAAC,6CAA6C,CAAC;MACtD;IACF;EACF,CAAC;EAED,MAAM0D,oBAAoB,GAAGA,CAAA,KAAM;IACjC,MAAMC,YAAY,GAAG9G,SAAS,CAAC+G,MAAM,CAAC,CAACC,GAAG,EAAErC,IAAI,KAAK;MACnD,IAAI,CAACqC,GAAG,CAACrC,IAAI,CAAC5D,QAAQ,CAAC,EAAEiG,GAAG,CAACrC,IAAI,CAAC5D,QAAQ,CAAC,GAAG,EAAE;MAChDiG,GAAG,CAACrC,IAAI,CAAC5D,QAAQ,CAAC,CAACkG,IAAI,CAACtC,IAAI,CAAC;MAC7B,OAAOqC,GAAG;IACZ,CAAC,EAAE,CAAC,CAAC,CAAC;IAEN,oBACEnI,OAAA;MAAK4E,SAAS,EAAC,yBAAyB;MAAAC,QAAA,gBACtC7E,OAAA;QAAK4E,SAAS,EAAC,gBAAgB;QAAAC,QAAA,gBAC7B7E,OAAA;UAAA6E,QAAA,GAAI,gCAAoB,EAAC1D,SAAS,CAAC2D,MAAM,EAAC,SAAO;QAAA;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAI,CAAC,eACtDlF,OAAA;UACE4E,SAAS,EAAC,cAAc;UACxBgC,OAAO,EAAEA,CAAA,KAAMvE,cAAc,CAAC,CAACD,WAAW,CAAE;UAAAyC,QAAA,EAE3CzC,WAAW,GAAG,UAAU,GAAG;QAAgB;UAAA2C,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACtC,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACN,CAAC,EAEL9C,WAAW,iBACVpC,OAAA;QAAK4E,SAAS,EAAC,eAAe;QAAAC,QAAA,gBAC5B7E,OAAA;UAAA6E,QAAA,EAAI;QAAiB;UAAAE,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAI,CAAC,eAC1BlF,OAAA;UAAMqI,QAAQ,EAAEhB,aAAc;UAAAxC,QAAA,gBAC5B7E,OAAA;YAAK4E,SAAS,EAAC,UAAU;YAAAC,QAAA,gBACvB7E,OAAA;cAAK4E,SAAS,EAAC,YAAY;cAAAC,QAAA,gBACzB7E,OAAA;gBAAA6E,QAAA,EAAO;cAAM;gBAAAE,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAO,CAAC,eACrBlF,OAAA;gBACEsI,IAAI,EAAC,MAAM;gBACXrB,KAAK,EAAEpF,OAAO,CAACE,IAAK;gBACpBmF,QAAQ,EAAGC,CAAC,IAAKrF,UAAU,CAAC;kBAAC,GAAGD,OAAO;kBAAEE,IAAI,EAAEoF,CAAC,CAACC,MAAM,CAACH;gBAAK,CAAC,CAAE;gBAChEsB,QAAQ;cAAA;gBAAAxD,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACT,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACC,CAAC,eACNlF,OAAA;cAAK4E,SAAS,EAAC,YAAY;cAAAC,QAAA,gBACzB7E,OAAA;gBAAA6E,QAAA,EAAO;cAAO;gBAAAE,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAO,CAAC,eACtBlF,OAAA;gBACEsI,IAAI,EAAC,QAAQ;gBACbE,IAAI,EAAC,MAAM;gBACXvB,KAAK,EAAEpF,OAAO,CAACI,KAAM;gBACrBiF,QAAQ,EAAGC,CAAC,IAAKrF,UAAU,CAAC;kBAAC,GAAGD,OAAO;kBAAEI,KAAK,EAAEkF,CAAC,CAACC,MAAM,CAACH;gBAAK,CAAC,CAAE;gBACjEsB,QAAQ;cAAA;gBAAAxD,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACT,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACC,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACH,CAAC,eACNlF,OAAA;YAAK4E,SAAS,EAAC,YAAY;YAAAC,QAAA,gBACzB7E,OAAA;cAAA6E,QAAA,EAAO;YAAU;cAAAE,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAO,CAAC,eACzBlF,OAAA;cACEiH,KAAK,EAAEpF,OAAO,CAACK,QAAS;cACxBgF,QAAQ,EAAGC,CAAC,IAAKrF,UAAU,CAAC;gBAAC,GAAGD,OAAO;gBAAEK,QAAQ,EAAEiF,CAAC,CAACC,MAAM,CAACH;cAAK,CAAC,CAAE;cAAApC,QAAA,gBAEpE7E,OAAA;gBAAQiH,KAAK,EAAC,YAAY;gBAAApC,QAAA,EAAC;cAAU;gBAAAE,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAQ,CAAC,eAC9ClF,OAAA;gBAAQiH,KAAK,EAAC,OAAO;gBAAApC,QAAA,EAAC;cAAY;gBAAAE,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAQ,CAAC,eAC3ClF,OAAA;gBAAQiH,KAAK,EAAC,UAAU;gBAAApC,QAAA,EAAC;cAAQ;gBAAAE,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAQ,CAAC,eAC1ClF,OAAA;gBAAQiH,KAAK,EAAC,WAAW;gBAAApC,QAAA,EAAC;cAAS;gBAAAE,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAQ,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACtC,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACN,CAAC,eACNlF,OAAA;YAAK4E,SAAS,EAAC,YAAY;YAAAC,QAAA,gBACzB7E,OAAA;cAAA6E,QAAA,EAAO;YAAW;cAAAE,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAO,CAAC,eAC1BlF,OAAA;cACEiH,KAAK,EAAEpF,OAAO,CAACG,WAAY;cAC3BkF,QAAQ,EAAGC,CAAC,IAAKrF,UAAU,CAAC;gBAAC,GAAGD,OAAO;gBAAEG,WAAW,EAAEmF,CAAC,CAACC,MAAM,CAACH;cAAK,CAAC,CAAE;cACvEwB,IAAI,EAAC;YAAG;cAAA1D,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACT,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACC,CAAC,eACNlF,OAAA;YAAK4E,SAAS,EAAC,cAAc;YAAAC,QAAA,gBAC3B7E,OAAA;cAAQsI,IAAI,EAAC,QAAQ;cAAC1D,SAAS,EAAC,UAAU;cAAAC,QAAA,EAAC;YAAS;cAAAE,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAQ,CAAC,eAC7DlF,OAAA;cAAQsI,IAAI,EAAC,QAAQ;cAAC1B,OAAO,EAAEA,CAAA,KAAMvE,cAAc,CAAC,KAAK,CAAE;cAACuC,SAAS,EAAC,YAAY;cAAAC,QAAA,EAAC;YAAM;cAAAE,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAQ,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAC/F,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACF,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACJ,CACN,EAEAwD,MAAM,CAACC,IAAI,CAACV,YAAY,CAAC,CAAClF,GAAG,CAACb,QAAQ,iBACrClC,OAAA;QAAoB4E,SAAS,EAAC,uBAAuB;QAAAC,QAAA,gBACnD7E,OAAA;UAAI4E,SAAS,EAAC,gBAAgB;UAAAC,QAAA,GAC3B3C,QAAQ,KAAK,YAAY,IAAI,IAAI,EACjCA,QAAQ,KAAK,OAAO,IAAI,KAAK,EAC7BA,QAAQ,KAAK,UAAU,IAAI,IAAI,EAC/BA,QAAQ,KAAK,WAAW,IAAI,IAAI,EAChCA,QAAQ,CAAC0G,MAAM,CAAC,CAAC,CAAC,CAAChD,WAAW,CAAC,CAAC,GAAG1D,QAAQ,CAAC2G,KAAK,CAAC,CAAC,CAAC,EAAC,IAAE,EAACZ,YAAY,CAAC/F,QAAQ,CAAC,CAAC4C,MAAM,EAAC,GACzF;QAAA;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAI,CAAC,eAELlF,OAAA;UAAK4E,SAAS,EAAC,iBAAiB;UAAAC,QAAA,EAC7BoD,YAAY,CAAC/F,QAAQ,CAAC,CAACa,GAAG,CAAC+C,IAAI,iBAC9B9F,OAAA;YAAoB4E,SAAS,EAAC,gBAAgB;YAAAC,QAAA,EAC3ClD,WAAW,KAAKmE,IAAI,CAAC9C,GAAG,gBACvBhD,OAAA,CAAC8I,YAAY;cACXhD,IAAI,EAAEA,IAAK;cACXiD,MAAM,EAAGlF,IAAI,IAAK2D,gBAAgB,CAAC1B,IAAI,CAAC9C,GAAG,EAAEa,IAAI,CAAE;cACnDmF,QAAQ,EAAEA,CAAA,KAAMpH,cAAc,CAAC,IAAI;YAAE;cAAAmD,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACtC,CAAC,gBAEFlF,OAAA,CAAAE,SAAA;cAAA2E,QAAA,gBACE7E,OAAA;gBAAK4E,SAAS,EAAC,aAAa;gBAAAC,QAAA,gBAC1B7E,OAAA;kBAAA6E,QAAA,EAAKiB,IAAI,CAAC/D;gBAAI;kBAAAgD,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAK,CAAC,eACpBlF,OAAA;kBAAM4E,SAAS,EAAC,YAAY;kBAAAC,QAAA,GAAC,GAAC,EAACiB,IAAI,CAAC7D,KAAK,CAACiE,OAAO,CAAC,CAAC,CAAC;gBAAA;kBAAAnB,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAO,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACzD,CAAC,eACNlF,OAAA;gBAAG4E,SAAS,EAAC,kBAAkB;gBAAAC,QAAA,EAAEiB,IAAI,CAAC9D;cAAW;gBAAA+C,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAI,CAAC,eACtDlF,OAAA;gBAAK4E,SAAS,EAAC,aAAa;gBAAAC,QAAA,eAC1B7E,OAAA;kBAAM4E,SAAS,EAAE,gBAAgBkB,IAAI,CAAC3D,SAAS,GAAG,WAAW,GAAG,aAAa,EAAG;kBAAA0C,QAAA,EAC7EiB,IAAI,CAAC3D,SAAS,GAAG,aAAa,GAAG;gBAAe;kBAAA4C,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAC7C;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACJ,CAAC,eACNlF,OAAA;gBAAK4E,SAAS,EAAC,cAAc;gBAAAC,QAAA,gBAC3B7E,OAAA;kBACE4E,SAAS,EAAC,UAAU;kBACpBgC,OAAO,EAAEA,CAAA,KAAMhF,cAAc,CAACkE,IAAI,CAAC9C,GAAG,CAAE;kBAAA6B,QAAA,EACzC;gBAED;kBAAAE,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAQ,CAAC,eACTlF,OAAA;kBACE4E,SAAS,EAAC,YAAY;kBACtBgC,OAAO,EAAEA,CAAA,KAAMgB,gBAAgB,CAAC9B,IAAI,CAAC9C,GAAG,CAAE;kBAAA6B,QAAA,EAC3C;gBAED;kBAAAE,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAQ,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACN,CAAC;YAAA,eACN;UACH,GAlCOY,IAAI,CAAC9C,GAAG;YAAA+B,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAmCb,CACN;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACC,CAAC;MAAA,GAhDEhD,QAAQ;QAAA6C,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAiDb,CACN,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACC,CAAC;EAEV,CAAC;;EAED;EACA,MAAM4D,YAAY,GAAGA,CAAC;IAAEhD,IAAI;IAAEiD,MAAM;IAAEC;EAAS,CAAC,KAAK;IACnD;IACA,MAAMC,YAAY,GAAI9B,CAAC,IAAK;MAC1BA,CAAC,CAACG,cAAc,CAAC,CAAC;MAClB,MAAM4B,QAAQ,GAAG,IAAIC,QAAQ,CAAChC,CAAC,CAACC,MAAM,CAAC;MACvC,MAAMM,WAAW,GAAG;QAClB3F,IAAI,EAAEmH,QAAQ,CAACtF,GAAG,CAAC,MAAM,CAAC;QAC1B5B,WAAW,EAAEkH,QAAQ,CAACtF,GAAG,CAAC,aAAa,CAAC;QACxC3B,KAAK,EAAEsF,UAAU,CAAC2B,QAAQ,CAACtF,GAAG,CAAC,OAAO,CAAC,CAAC;QACxC1B,QAAQ,EAAEgH,QAAQ,CAACtF,GAAG,CAAC,UAAU,CAAC;QAClCzB,SAAS,EAAE+G,QAAQ,CAACtF,GAAG,CAAC,WAAW,CAAC,KAAK;MAC3C,CAAC;MACDmF,MAAM,CAACrB,WAAW,CAAC;IACrB,CAAC;IAED,oBACE1H,OAAA;MAAMqI,QAAQ,EAAEY,YAAa;MAACrE,SAAS,EAAC,gBAAgB;MAAAC,QAAA,gBACtD7E,OAAA;QAAK4E,SAAS,EAAC,YAAY;QAAAC,QAAA,eACzB7E,OAAA;UACEsI,IAAI,EAAC,MAAM;UACXvG,IAAI,EAAC,MAAM;UACXqH,YAAY,EAAEtD,IAAI,CAAC/D,IAAK;UACxBwG,QAAQ;UACRc,WAAW,EAAC;QAAW;UAAAtE,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACxB;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACC,CAAC,eACNlF,OAAA;QAAK4E,SAAS,EAAC,YAAY;QAAAC,QAAA,eACzB7E,OAAA;UACEsI,IAAI,EAAC,QAAQ;UACbvG,IAAI,EAAC,OAAO;UACZyG,IAAI,EAAC,MAAM;UACXY,YAAY,EAAEtD,IAAI,CAAC7D,KAAM;UACzBsG,QAAQ;UACRc,WAAW,EAAC;QAAO;UAAAtE,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACpB;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACC,CAAC,eACNlF,OAAA;QAAK4E,SAAS,EAAC,YAAY;QAAAC,QAAA,eACzB7E,OAAA;UAAQ+B,IAAI,EAAC,UAAU;UAACqH,YAAY,EAAEtD,IAAI,CAAC5D,QAAS;UAAA2C,QAAA,gBAClD7E,OAAA;YAAQiH,KAAK,EAAC,YAAY;YAAApC,QAAA,EAAC;UAAU;YAAAE,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAQ,CAAC,eAC9ClF,OAAA;YAAQiH,KAAK,EAAC,OAAO;YAAApC,QAAA,EAAC;UAAY;YAAAE,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAQ,CAAC,eAC3ClF,OAAA;YAAQiH,KAAK,EAAC,UAAU;YAAApC,QAAA,EAAC;UAAQ;YAAAE,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAQ,CAAC,eAC1ClF,OAAA;YAAQiH,KAAK,EAAC,WAAW;YAAApC,QAAA,EAAC;UAAS;YAAAE,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAQ,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACtC;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACN,CAAC,eACNlF,OAAA;QAAK4E,SAAS,EAAC,YAAY;QAAAC,QAAA,eACzB7E,OAAA;UACE+B,IAAI,EAAC,aAAa;UAClBqH,YAAY,EAAEtD,IAAI,CAAC9D,WAAY;UAC/ByG,IAAI,EAAC,GAAG;UACRY,WAAW,EAAC;QAAa;UAAAtE,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAC1B;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACC,CAAC,eACNlF,OAAA;QAAK4E,SAAS,EAAC,YAAY;QAAAC,QAAA,eACzB7E,OAAA;UAAA6E,QAAA,gBACE7E,OAAA;YACEsI,IAAI,EAAC,UAAU;YACfvG,IAAI,EAAC,WAAW;YAChBuH,cAAc,EAAExD,IAAI,CAAC3D;UAAU;YAAA4C,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAChC,CAAC,aAEJ;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAO;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACL,CAAC,eACNlF,OAAA;QAAK4E,SAAS,EAAC,cAAc;QAAAC,QAAA,gBAC3B7E,OAAA;UAAQsI,IAAI,EAAC,QAAQ;UAAC1D,SAAS,EAAC,UAAU;UAAAC,QAAA,EAAC;QAAI;UAAAE,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAQ,CAAC,eACxDlF,OAAA;UAAQsI,IAAI,EAAC,QAAQ;UAAC1B,OAAO,EAAEoC,QAAS;UAACpE,SAAS,EAAC,YAAY;UAAAC,QAAA,EAAC;QAAM;UAAAE,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAQ,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAC5E,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACF,CAAC;EAEX,CAAC;EAED,IAAIzD,OAAO,EAAE;IACX,oBACEzB,OAAA;MAAK4E,SAAS,EAAC,mBAAmB;MAAAC,QAAA,eAChC7E,OAAA;QAAK4E,SAAS,EAAC,SAAS;QAAAC,QAAA,gBACtB7E,OAAA;UAAK4E,SAAS,EAAC;QAAS;UAAAG,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAM,CAAC,eAC/BlF,OAAA;UAAA6E,QAAA,EAAG;QAA4B;UAAAE,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAG,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAChC;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH,CAAC;EAEV;EAEA,oBACElF,OAAA;IAAK4E,SAAS,EAAC,mBAAmB;IAAAC,QAAA,gBAChC7E,OAAA;MAAK4E,SAAS,EAAC,gBAAgB;MAAAC,QAAA,gBAC7B7E,OAAA;QAAK4E,SAAS,EAAC,aAAa;QAAAC,QAAA,gBAC1B7E,OAAA;UAAA6E,QAAA,EAAI;QAAuB;UAAAE,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAI,CAAC,eAChClF,OAAA;UAAA6E,QAAA,GAAG,WAAS,EAACnE,IAAI,CAACqB,IAAI;QAAA;UAAAgD,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAI,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACxB,CAAC,eACNlF,OAAA;QAAK4E,SAAS,EAAC,cAAc;QAAAC,QAAA,eAC3B7E,OAAA;UAAQ4E,SAAS,EAAC,YAAY;UAACgC,OAAO,EAAEjG,QAAS;UAAAkE,QAAA,EAAC;QAElD;UAAAE,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAQ;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACN,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH,CAAC,eAENlF,OAAA;MAAK4E,SAAS,EAAC,YAAY;MAAAC,QAAA,gBACzB7E,OAAA;QACE4E,SAAS,EAAE/D,SAAS,KAAK,QAAQ,GAAG,QAAQ,GAAG,EAAG;QAClD+F,OAAO,EAAEA,CAAA,KAAM9F,YAAY,CAAC,QAAQ,CAAE;QAAA+D,QAAA,EACvC;MAED;QAAAE,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAQ,CAAC,eACTlF,OAAA;QACE4E,SAAS,EAAE/D,SAAS,KAAK,eAAe,GAAG,QAAQ,GAAG,EAAG;QACzD+F,OAAO,EAAEA,CAAA,KAAM9F,YAAY,CAAC,eAAe,CAAE;QAAA+D,QAAA,GAC9C,gBACe,EAACxD,iBAAiB,CAACyD,MAAM,GAAG,CAAC,iBAAI9E,OAAA;UAAM4E,SAAS,EAAC,oBAAoB;UAAAC,QAAA,EAAExD,iBAAiB,CAACyD;QAAM;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAO,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAC/G,CAAC,eACTlF,OAAA;QACE4E,SAAS,EAAE/D,SAAS,KAAK,QAAQ,GAAG,QAAQ,GAAG,EAAG;QAClD+F,OAAO,EAAEA,CAAA,KAAM9F,YAAY,CAAC,QAAQ,CAAE;QAAA+D,QAAA,EACvC;MAED;QAAAE,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAQ,CAAC,eACTlF,OAAA;QACE4E,SAAS,EAAE/D,SAAS,KAAK,MAAM,GAAG,QAAQ,GAAG,EAAG;QAChD+F,OAAO,EAAEA,CAAA,KAAM9F,YAAY,CAAC,MAAM,CAAE;QAAA+D,QAAA,EACrC;MAED;QAAAE,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAQ,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACN,CAAC,eAENlF,OAAA;MAAK4E,SAAS,EAAC,aAAa;MAAAC,QAAA,GACzBhE,SAAS,KAAK,QAAQ,IAAI8D,YAAY,CAAC,CAAC,EACxC9D,SAAS,KAAK,eAAe,IAAIgG,kBAAkB,CAAC,CAAC,EACrDhG,SAAS,KAAK,QAAQ,IAAIkG,YAAY,CAAC,CAAC,EACxClG,SAAS,KAAK,MAAM,IAAImH,oBAAoB,CAAC,CAAC;IAAA;MAAAjD,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAC5C,CAAC;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACH,CAAC;AAEV,CAAC;AAACtE,EAAA,CAjpBIH,gBAAgB;AAAA8I,EAAA,GAAhB9I,gBAAgB;AAmpBtB,eAAeA,gBAAgB;AAAC,IAAA8I,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}