{"ast": null, "code": "var _jsxFileName = \"C:\\\\restraunat managment system\\\\frontend\\\\src\\\\components\\\\Login.js\",\n  _s = $RefreshSig$();\nimport React, { useState } from 'react';\nimport axios from 'axios';\nimport { jsxDEV as _jsxDEV, Fragment as _Fragment } from \"react/jsx-dev-runtime\";\nconst API_BASE_URL = process.env.REACT_APP_API_BASE_URL;\nconst Login = ({\n  onLogin\n}) => {\n  _s();\n  const [credentials, setCredentials] = useState({\n    username: '',\n    password: ''\n  });\n  const [loading, setLoading] = useState(false);\n  const [error, setError] = useState('');\n  const handleSubmit = async e => {\n    e.preventDefault();\n    setLoading(true);\n    setError('');\n    try {\n      const response = await axios.post(`${API_BASE_URL}/api/auth/login`, credentials);\n      if (response.data.success) {\n        // Store token and user info\n        localStorage.setItem('token', response.data.token);\n        localStorage.setItem('user', JSON.stringify(response.data.user));\n\n        // Call parent callback\n        onLogin(response.data.user);\n      }\n    } catch (error) {\n      var _error$response, _error$response$data;\n      console.error('Login error:', error);\n      setError(((_error$response = error.response) === null || _error$response === void 0 ? void 0 : (_error$response$data = _error$response.data) === null || _error$response$data === void 0 ? void 0 : _error$response$data.message) || 'Login failed. Please try again.');\n    } finally {\n      setLoading(false);\n    }\n  };\n  const handleChange = e => {\n    setCredentials({\n      ...credentials,\n      [e.target.name]: e.target.value\n    });\n  };\n  return /*#__PURE__*/_jsxDEV(\"div\", {\n    className: \"login-container\",\n    children: /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"login-card\",\n      children: [/*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"login-header\",\n        children: [/*#__PURE__*/_jsxDEV(\"h1\", {\n          children: \"\\uD83C\\uDF7D\\uFE0F Restaurant System\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 49,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n          children: \"Please sign in to continue\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 50,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 48,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"form\", {\n        onSubmit: handleSubmit,\n        className: \"login-form\",\n        children: [error && /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"error-message\",\n          children: [\"\\u26A0\\uFE0F \", error]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 55,\n          columnNumber: 13\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"form-group\",\n          children: [/*#__PURE__*/_jsxDEV(\"label\", {\n            htmlFor: \"username\",\n            children: \"Username\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 61,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"input\", {\n            type: \"text\",\n            id: \"username\",\n            name: \"username\",\n            value: credentials.username,\n            onChange: handleChange,\n            required: true,\n            placeholder: \"Enter your username\",\n            disabled: loading\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 62,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 60,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"form-group\",\n          children: [/*#__PURE__*/_jsxDEV(\"label\", {\n            htmlFor: \"password\",\n            children: \"Password\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 75,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"input\", {\n            type: \"password\",\n            id: \"password\",\n            name: \"password\",\n            value: credentials.password,\n            onChange: handleChange,\n            required: true,\n            placeholder: \"Enter your password\",\n            disabled: loading\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 76,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 74,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n          type: \"submit\",\n          className: \"login-btn\",\n          disabled: loading || !credentials.username || !credentials.password,\n          children: loading ? /*#__PURE__*/_jsxDEV(_Fragment, {\n            children: [/*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"spinner\",\n              style: {\n                width: '20px',\n                height: '20px',\n                marginRight: '10px'\n              }\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 95,\n              columnNumber: 17\n            }, this), \"Signing in...\"]\n          }, void 0, true) : 'Sign In'\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 88,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 53,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"login-help\",\n        children: /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"demo-accounts\",\n          children: [/*#__PURE__*/_jsxDEV(\"h4\", {\n            children: \"Demo Accounts:\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 106,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"demo-account\",\n            children: [/*#__PURE__*/_jsxDEV(\"strong\", {\n              children: \"Admin:\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 108,\n              columnNumber: 15\n            }, this), \" admin / admin123\"]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 107,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"demo-account\",\n            children: [/*#__PURE__*/_jsxDEV(\"strong\", {\n              children: \"Manager:\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 111,\n              columnNumber: 15\n            }, this), \" manager / manager123\"]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 110,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"demo-account\",\n            children: [/*#__PURE__*/_jsxDEV(\"strong\", {\n              children: \"Kitchen:\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 114,\n              columnNumber: 15\n            }, this), \" kitchen / kitchen123\"]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 113,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 105,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 104,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 47,\n      columnNumber: 7\n    }, this)\n  }, void 0, false, {\n    fileName: _jsxFileName,\n    lineNumber: 46,\n    columnNumber: 5\n  }, this);\n};\n_s(Login, \"NWgBqVCq+tRwO9l3Fl4lBlHVvck=\");\n_c = Login;\nexport default Login;\nvar _c;\n$RefreshReg$(_c, \"Login\");", "map": {"version": 3, "names": ["React", "useState", "axios", "jsxDEV", "_jsxDEV", "Fragment", "_Fragment", "API_BASE_URL", "process", "env", "REACT_APP_API_BASE_URL", "<PERSON><PERSON>", "onLogin", "_s", "credentials", "setCredentials", "username", "password", "loading", "setLoading", "error", "setError", "handleSubmit", "e", "preventDefault", "response", "post", "data", "success", "localStorage", "setItem", "token", "JSON", "stringify", "user", "_error$response", "_error$response$data", "console", "message", "handleChange", "target", "name", "value", "className", "children", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "onSubmit", "htmlFor", "type", "id", "onChange", "required", "placeholder", "disabled", "style", "width", "height", "marginRight", "_c", "$RefreshReg$"], "sources": ["C:/restraunat managment system/frontend/src/components/Login.js"], "sourcesContent": ["import React, { useState } from 'react';\nimport axios from 'axios';\n\nconst API_BASE_URL = process.env.REACT_APP_API_BASE_URL;\n\nconst Login = ({ onLogin }) => {\n  const [credentials, setCredentials] = useState({\n    username: '',\n    password: ''\n  });\n  const [loading, setLoading] = useState(false);\n  const [error, setError] = useState('');\n\n  const handleSubmit = async (e) => {\n    e.preventDefault();\n    setLoading(true);\n    setError('');\n\n    try {\n      const response = await axios.post(`${API_BASE_URL}/api/auth/login`, credentials);\n      \n      if (response.data.success) {\n        // Store token and user info\n        localStorage.setItem('token', response.data.token);\n        localStorage.setItem('user', JSON.stringify(response.data.user));\n        \n        // Call parent callback\n        onLogin(response.data.user);\n      }\n    } catch (error) {\n      console.error('Login error:', error);\n      setError(error.response?.data?.message || 'Login failed. Please try again.');\n    } finally {\n      setLoading(false);\n    }\n  };\n\n  const handleChange = (e) => {\n    setCredentials({\n      ...credentials,\n      [e.target.name]: e.target.value\n    });\n  };\n\n  return (\n    <div className=\"login-container\">\n      <div className=\"login-card\">\n        <div className=\"login-header\">\n          <h1>🍽️ Restaurant System</h1>\n          <p>Please sign in to continue</p>\n        </div>\n\n        <form onSubmit={handleSubmit} className=\"login-form\">\n          {error && (\n            <div className=\"error-message\">\n              ⚠️ {error}\n            </div>\n          )}\n\n          <div className=\"form-group\">\n            <label htmlFor=\"username\">Username</label>\n            <input\n              type=\"text\"\n              id=\"username\"\n              name=\"username\"\n              value={credentials.username}\n              onChange={handleChange}\n              required\n              placeholder=\"Enter your username\"\n              disabled={loading}\n            />\n          </div>\n\n          <div className=\"form-group\">\n            <label htmlFor=\"password\">Password</label>\n            <input\n              type=\"password\"\n              id=\"password\"\n              name=\"password\"\n              value={credentials.password}\n              onChange={handleChange}\n              required\n              placeholder=\"Enter your password\"\n              disabled={loading}\n            />\n          </div>\n\n          <button \n            type=\"submit\" \n            className=\"login-btn\"\n            disabled={loading || !credentials.username || !credentials.password}\n          >\n            {loading ? (\n              <>\n                <div className=\"spinner\" style={{width: '20px', height: '20px', marginRight: '10px'}}></div>\n                Signing in...\n              </>\n            ) : (\n              'Sign In'\n            )}\n          </button>\n        </form>\n\n        <div className=\"login-help\">\n          <div className=\"demo-accounts\">\n            <h4>Demo Accounts:</h4>\n            <div className=\"demo-account\">\n              <strong>Admin:</strong> admin / admin123\n            </div>\n            <div className=\"demo-account\">\n              <strong>Manager:</strong> manager / manager123\n            </div>\n            <div className=\"demo-account\">\n              <strong>Kitchen:</strong> kitchen / kitchen123\n            </div>\n          </div>\n        </div>\n      </div>\n    </div>\n  );\n};\n\nexport default Login;\n"], "mappings": ";;AAAA,OAAOA,KAAK,IAAIC,QAAQ,QAAQ,OAAO;AACvC,OAAOC,KAAK,MAAM,OAAO;AAAC,SAAAC,MAAA,IAAAC,OAAA,EAAAC,QAAA,IAAAC,SAAA;AAE1B,MAAMC,YAAY,GAAGC,OAAO,CAACC,GAAG,CAACC,sBAAsB;AAEvD,MAAMC,KAAK,GAAGA,CAAC;EAAEC;AAAQ,CAAC,KAAK;EAAAC,EAAA;EAC7B,MAAM,CAACC,WAAW,EAAEC,cAAc,CAAC,GAAGd,QAAQ,CAAC;IAC7Ce,QAAQ,EAAE,EAAE;IACZC,QAAQ,EAAE;EACZ,CAAC,CAAC;EACF,MAAM,CAACC,OAAO,EAAEC,UAAU,CAAC,GAAGlB,QAAQ,CAAC,KAAK,CAAC;EAC7C,MAAM,CAACmB,KAAK,EAAEC,QAAQ,CAAC,GAAGpB,QAAQ,CAAC,EAAE,CAAC;EAEtC,MAAMqB,YAAY,GAAG,MAAOC,CAAC,IAAK;IAChCA,CAAC,CAACC,cAAc,CAAC,CAAC;IAClBL,UAAU,CAAC,IAAI,CAAC;IAChBE,QAAQ,CAAC,EAAE,CAAC;IAEZ,IAAI;MACF,MAAMI,QAAQ,GAAG,MAAMvB,KAAK,CAACwB,IAAI,CAAC,GAAGnB,YAAY,iBAAiB,EAAEO,WAAW,CAAC;MAEhF,IAAIW,QAAQ,CAACE,IAAI,CAACC,OAAO,EAAE;QACzB;QACAC,YAAY,CAACC,OAAO,CAAC,OAAO,EAAEL,QAAQ,CAACE,IAAI,CAACI,KAAK,CAAC;QAClDF,YAAY,CAACC,OAAO,CAAC,MAAM,EAAEE,IAAI,CAACC,SAAS,CAACR,QAAQ,CAACE,IAAI,CAACO,IAAI,CAAC,CAAC;;QAEhE;QACAtB,OAAO,CAACa,QAAQ,CAACE,IAAI,CAACO,IAAI,CAAC;MAC7B;IACF,CAAC,CAAC,OAAOd,KAAK,EAAE;MAAA,IAAAe,eAAA,EAAAC,oBAAA;MACdC,OAAO,CAACjB,KAAK,CAAC,cAAc,EAAEA,KAAK,CAAC;MACpCC,QAAQ,CAAC,EAAAc,eAAA,GAAAf,KAAK,CAACK,QAAQ,cAAAU,eAAA,wBAAAC,oBAAA,GAAdD,eAAA,CAAgBR,IAAI,cAAAS,oBAAA,uBAApBA,oBAAA,CAAsBE,OAAO,KAAI,iCAAiC,CAAC;IAC9E,CAAC,SAAS;MACRnB,UAAU,CAAC,KAAK,CAAC;IACnB;EACF,CAAC;EAED,MAAMoB,YAAY,GAAIhB,CAAC,IAAK;IAC1BR,cAAc,CAAC;MACb,GAAGD,WAAW;MACd,CAACS,CAAC,CAACiB,MAAM,CAACC,IAAI,GAAGlB,CAAC,CAACiB,MAAM,CAACE;IAC5B,CAAC,CAAC;EACJ,CAAC;EAED,oBACEtC,OAAA;IAAKuC,SAAS,EAAC,iBAAiB;IAAAC,QAAA,eAC9BxC,OAAA;MAAKuC,SAAS,EAAC,YAAY;MAAAC,QAAA,gBACzBxC,OAAA;QAAKuC,SAAS,EAAC,cAAc;QAAAC,QAAA,gBAC3BxC,OAAA;UAAAwC,QAAA,EAAI;QAAqB;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAI,CAAC,eAC9B5C,OAAA;UAAAwC,QAAA,EAAG;QAA0B;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAG,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAC9B,CAAC,eAEN5C,OAAA;QAAM6C,QAAQ,EAAE3B,YAAa;QAACqB,SAAS,EAAC,YAAY;QAAAC,QAAA,GACjDxB,KAAK,iBACJhB,OAAA;UAAKuC,SAAS,EAAC,eAAe;UAAAC,QAAA,GAAC,eAC1B,EAACxB,KAAK;QAAA;UAAAyB,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACN,CACN,eAED5C,OAAA;UAAKuC,SAAS,EAAC,YAAY;UAAAC,QAAA,gBACzBxC,OAAA;YAAO8C,OAAO,EAAC,UAAU;YAAAN,QAAA,EAAC;UAAQ;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAO,CAAC,eAC1C5C,OAAA;YACE+C,IAAI,EAAC,MAAM;YACXC,EAAE,EAAC,UAAU;YACbX,IAAI,EAAC,UAAU;YACfC,KAAK,EAAE5B,WAAW,CAACE,QAAS;YAC5BqC,QAAQ,EAAEd,YAAa;YACvBe,QAAQ;YACRC,WAAW,EAAC,qBAAqB;YACjCC,QAAQ,EAAEtC;UAAQ;YAAA2B,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACnB,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACC,CAAC,eAEN5C,OAAA;UAAKuC,SAAS,EAAC,YAAY;UAAAC,QAAA,gBACzBxC,OAAA;YAAO8C,OAAO,EAAC,UAAU;YAAAN,QAAA,EAAC;UAAQ;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAO,CAAC,eAC1C5C,OAAA;YACE+C,IAAI,EAAC,UAAU;YACfC,EAAE,EAAC,UAAU;YACbX,IAAI,EAAC,UAAU;YACfC,KAAK,EAAE5B,WAAW,CAACG,QAAS;YAC5BoC,QAAQ,EAAEd,YAAa;YACvBe,QAAQ;YACRC,WAAW,EAAC,qBAAqB;YACjCC,QAAQ,EAAEtC;UAAQ;YAAA2B,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACnB,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACC,CAAC,eAEN5C,OAAA;UACE+C,IAAI,EAAC,QAAQ;UACbR,SAAS,EAAC,WAAW;UACrBa,QAAQ,EAAEtC,OAAO,IAAI,CAACJ,WAAW,CAACE,QAAQ,IAAI,CAACF,WAAW,CAACG,QAAS;UAAA2B,QAAA,EAEnE1B,OAAO,gBACNd,OAAA,CAAAE,SAAA;YAAAsC,QAAA,gBACExC,OAAA;cAAKuC,SAAS,EAAC,SAAS;cAACc,KAAK,EAAE;gBAACC,KAAK,EAAE,MAAM;gBAAEC,MAAM,EAAE,MAAM;gBAAEC,WAAW,EAAE;cAAM;YAAE;cAAAf,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAM,CAAC,iBAE9F;UAAA,eAAE,CAAC,GAEH;QACD;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACK,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACL,CAAC,eAEP5C,OAAA;QAAKuC,SAAS,EAAC,YAAY;QAAAC,QAAA,eACzBxC,OAAA;UAAKuC,SAAS,EAAC,eAAe;UAAAC,QAAA,gBAC5BxC,OAAA;YAAAwC,QAAA,EAAI;UAAc;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAI,CAAC,eACvB5C,OAAA;YAAKuC,SAAS,EAAC,cAAc;YAAAC,QAAA,gBAC3BxC,OAAA;cAAAwC,QAAA,EAAQ;YAAM;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAQ,CAAC,qBACzB;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAK,CAAC,eACN5C,OAAA;YAAKuC,SAAS,EAAC,cAAc;YAAAC,QAAA,gBAC3BxC,OAAA;cAAAwC,QAAA,EAAQ;YAAQ;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAQ,CAAC,yBAC3B;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAK,CAAC,eACN5C,OAAA;YAAKuC,SAAS,EAAC,cAAc;YAAAC,QAAA,gBAC3BxC,OAAA;cAAAwC,QAAA,EAAQ;YAAQ;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAQ,CAAC,yBAC3B;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAK,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH;EAAC;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACH,CAAC;AAEV,CAAC;AAACnC,EAAA,CAnHIF,KAAK;AAAAkD,EAAA,GAALlD,KAAK;AAqHX,eAAeA,KAAK;AAAC,IAAAkD,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}