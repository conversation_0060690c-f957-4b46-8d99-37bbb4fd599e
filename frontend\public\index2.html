<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Flame & Fire - Restaurant Menu</title>
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0-beta3/css/all.min.css">
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
            font-family: 'Arial Black', 'Arial Bold', Gadget, sans-serif;
        }

        :root {
            --primary-red: #D32F2F;
            --dark-red: #B71C1C;
            --light-red: #FFCDD2;
            --accent-red: #FF5252;
            --black: #212121;
            --dark-gray: #424242;
            --light-gray: #E0E0E0;
            --white: #FFFFFF;
            --text-color: #212121;
        }

        body {
            background-color: #F5F5F5;
            color: var(--text-color);
            line-height: 1.4;
            font-weight: 700;
        }

        /* Header and Navigation */
        header {
            background-color: var(--primary-red);
            color: var(--white);
            padding: 1rem 0;
            position: sticky;
            top: 0;
            z-index: 100;
            box-shadow: 0 4px 12px rgba(0, 0, 0, 0.3);
            border-bottom: 4px solid var(--dark-red);
        }

        .container {
            width: 90%;
            max-width: 1200px;
            margin: 0 auto;
            padding: 0 15px;
        }

        .restaurant-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            flex-wrap: wrap;
        }

        .logo {
            font-size: 2.2rem;
            font-weight: 900;
            letter-spacing: 1px;
            text-transform: uppercase;
            display: flex;
            align-items: center;
            text-shadow: 2px 2px 0 var(--dark-red);
        }

        .logo i {
            margin-right: 0.5rem;
            color: var(--white);
            font-size: 1.8rem;
        }

        .nav-links {
            display: flex;
            list-style: none;
        }

        .nav-links li {
            margin-left: 1.5rem;
        }

        .nav-links a {
            color: var(--white);
            text-decoration: none;
            font-weight: 800;
            font-size: 1.1rem;
            text-transform: uppercase;
            letter-spacing: 1px;
            transition: all 0.2s;
            position: relative;
            padding: 5px 0;
        }

        .nav-links a::after {
            content: '';
            position: absolute;
            width: 0;
            height: 3px;
            bottom: -3px;
            left: 0;
            background-color: var(--white);
            transition: width 0.3s;
        }

        .nav-links a:hover::after {
            width: 100%;
        }

        /* Hero Section */
        .hero {
            background: linear-gradient(rgba(209, 47, 47, 0.9), rgba(183, 28, 28, 0.9)), url('https://images.unsplash.com/photo-1565299624946-b28f40a0ca4b?ixlib=rb-1.2.1&auto=format&fit=crop&w=1950&q=80');
            background-size: cover;
            background-position: center;
            height: 40vh;
            display: flex;
            align-items: center;
            justify-content: center;
            text-align: center;
            color: var(--white);
            border-bottom: 5px solid var(--dark-red);
        }

        .hero-content {
            max-width: 800px;
            padding: 0 20px;
        }

        .hero h1 {
            font-size: 4rem;
            margin-bottom: 1rem;
            text-transform: uppercase;
            letter-spacing: 3px;
            text-shadow: 3px 3px 0 var(--dark-red);
        }

        .hero p {
            font-size: 1.4rem;
            max-width: 600px;
            margin: 0 auto;
            text-transform: uppercase;
            letter-spacing: 2px;
        }

        /* Menu Section */
        .menu-section {
            padding: 3rem 0;
            background-color: var(--white);
        }

        .section-title {
            text-align: center;
            margin-bottom: 3rem;
            padding: 2rem 0;
            background-color: var(--primary-red);
            color: var(--white);
            clip-path: polygon(0 0, 100% 0, 100% 85%, 0 100%);
        }

        .section-title h2 {
            font-size: 3rem;
            margin-bottom: 0.5rem;
            text-transform: uppercase;
            letter-spacing: 2px;
            text-shadow: 2px 2px 0 var(--dark-red);
        }

        .section-title p {
            font-size: 1.2rem;
            max-width: 700px;
            margin: 0 auto;
            text-transform: uppercase;
            letter-spacing: 1px;
        }

        /* Category Tabs */
        .category-tabs {
            display: flex;
            justify-content: center;
            margin-bottom: 2rem;
            flex-wrap: wrap;
            gap: 1rem;
        }

        .category-tab {
            padding: 1rem 2rem;
            background-color: var(--light-gray);
            border: none;
            border-radius: 0;
            cursor: pointer;
            font-weight: 900;
            font-size: 1.1rem;
            text-transform: uppercase;
            letter-spacing: 1px;
            transition: all 0.3s;
            color: var(--dark-gray);
            position: relative;
            overflow: hidden;
        }

        .category-tab::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            background-color: var(--primary-red);
            transform: translateX(-100%);
            transition: transform 0.3s;
            z-index: -1;
        }

        .category-tab.active {
            color: var(--white);
            background-color: var(--primary-red);
        }

        .category-tab:hover::before {
            transform: translateX(0);
        }

        .category-tab:hover {
            color: var(--white);
            transform: translateY(-3px);
            box-shadow: 0 6px 12px rgba(0, 0, 0, 0.2);
        }

        /* Menu Categories */
        .menu-category {
            display: none;
            animation: fadeIn 0.5s;
        }

        .menu-category.active {
            display: block;
        }

        @keyframes fadeIn {
            from { opacity: 0; transform: translateY(20px); }
            to { opacity: 1; transform: translateY(0); }
        }

        .menu-items {
            display: grid;
            grid-template-columns: repeat(auto-fill, minmax(300px, 1fr));
            gap: 2rem;
        }

        .menu-item {
            background-color: var(--white);
            border-radius: 0;
            overflow: hidden;
            box-shadow: 0 8px 16px rgba(0, 0, 0, 0.1);
            transition: all 0.3s;
            border: 1px solid var(--light-gray);
            position: relative;
        }

        .menu-item::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            width: 5px;
            height: 100%;
            background-color: var(--primary-red);
            transform: scaleY(0);
            transition: transform 0.3s;
        }

        .menu-item:hover {
            transform: translateY(-8px);
            box-shadow: 0 12px 24px rgba(0, 0, 0, 0.2);
        }

        .menu-item:hover::before {
            transform: scaleY(1);
        }

        .menu-item-img {
            height: 200px;
            overflow: hidden;
            position: relative;
        }

        .menu-item-img img {
            width: 100%;
            height: 100%;
            object-fit: cover;
            transition: transform 0.5s;
        }

        .menu-item:hover .menu-item-img img {
            transform: scale(1.1);
        }

        .menu-item-content {
            padding: 1.5rem;
        }

        .menu-item-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 0.8rem;
            border-bottom: 2px solid var(--light-gray);
            padding-bottom: 0.8rem;
        }

        .menu-item-title {
            font-size: 1.4rem;
            font-weight: 900;
            color: var(--black);
            text-transform: uppercase;
            letter-spacing: 1px;
        }

        .menu-item-price {
            font-weight: 900;
            color: var(--primary-red);
            font-size: 1.3rem;
            background-color: var(--light-red);
            padding: 5px 10px;
            border-radius: 4px;
        }

        .menu-item-description {
            color: var(--dark-gray);
            font-size: 1rem;
            margin-bottom: 1rem;
            font-weight: 600;
        }

        .menu-item-footer {
            display: flex;
            justify-content: space-between;
            align-items: center;
        }

        .menu-item-rating {
            color: #FFC107;
            font-size: 1.1rem;
        }

        .add-to-cart {
            background-color: var(--primary-red);
            color: var(--white);
            border: none;
            padding: 0.8rem 1.5rem;
            border-radius: 0;
            cursor: pointer;
            font-weight: 900;
            font-size: 1rem;
            text-transform: uppercase;
            letter-spacing: 1px;
            transition: all 0.3s;
            position: relative;
            overflow: hidden;
            z-index: 1;
        }

        .add-to-cart::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            background-color: var(--dark-red);
            transform: translateY(100%);
            transition: transform 0.3s;
            z-index: -1;
        }

        .add-to-cart:hover::before {
            transform: translateY(0);
        }

        .add-to-cart:hover {
            transform: translateY(-3px);
            box-shadow: 0 6px 12px rgba(0, 0, 0, 0.2);
        }

        /* Footer */
        footer {
            background-color: var(--black);
            color: var(--white);
            padding: 3rem 0;
            text-align: center;
            border-top: 5px solid var(--primary-red);
        }

        .footer-content {
            display: flex;
            justify-content: space-between;
            flex-wrap: wrap;
            margin-bottom: 2rem;
        }

        .footer-section {
            flex: 1;
            min-width: 250px;
            margin-bottom: 2rem;
            padding: 0 1rem;
        }

        .footer-section h3 {
            margin-bottom: 1rem;
            color: var(--primary-red);
            font-size: 1.5rem;
            text-transform: uppercase;
            letter-spacing: 1px;
            font-weight: 900;
        }

        .footer-section p, .footer-section li {
            margin-bottom: 0.8rem;
            color: #E0E0E0;
            font-weight: 600;
        }

        .footer-section ul {
            list-style: none;
        }

        .social-links {
            display: flex;
            justify-content: center;
            margin-top: 1rem;
            gap: 1rem;
        }

        .social-links a {
            display: inline-block;
            width: 50px;
            height: 50px;
            background-color: var(--dark-gray);
            border-radius: 0;
            text-align: center;
            line-height: 50px;
            color: var(--white);
            font-size: 1.5rem;
            transition: all 0.3s;
            border: 2px solid transparent;
        }

        .social-links a:hover {
            background-color: var(--primary-red);
            border-color: var(--white);
            transform: translateY(-5px);
        }

        .copyright {
            padding-top: 2rem;
            border-top: 2px solid var(--dark-gray);
            font-size: 1rem;
            color: #BDBDBD;
            text-transform: uppercase;
            letter-spacing: 1px;
        }

        /* Mobile Menu Toggle */
        .mobile-toggle {
            display: none;
            font-size: 1.8rem;
            cursor: pointer;
        }

        /* Responsive Design */
        @media (max-width: 768px) {
            .restaurant-header {
                justify-content: space-between;
            }

            .nav-links {
                display: none;
                width: 100%;
                flex-direction: column;
                margin-top: 1rem;
                background-color: var(--dark-red);
                padding: 1rem;
                border-radius: 0;
            }

            .nav-links.active {
                display: flex;
            }

            .nav-links li {
                margin: 0.8rem 0;
            }

            .mobile-toggle {
                display: block;
            }

            .hero h1 {
                font-size: 2.5rem;
            }

            .hero p {
                font-size: 1rem;
            }

            .section-title h2 {
                font-size: 2rem;
            }

            .section-title p {
                font-size: 1rem;
            }

            .menu-items {
                grid-template-columns: 1fr;
            }

            .footer-content {
                flex-direction: column;
            }
        }
    </style>
</head>
<body>
    <!-- Header and Navigation -->
    <header>
        <div class="container">
            <div class="restaurant-header">
                <div class="logo">
                    <i class="fas fa-fire-alt"></i> Flame & Fire
                </div>
                <div class="mobile-toggle">
                    <i class="fas fa-bars"></i>
                </div>
                <ul class="nav-links">
                    <li><a href="#appetizers">Appetizers</a></li>
                    <li><a href="#mains">Main Courses</a></li>
                    <li><a href="#desserts">Desserts</a></li>
                    <li><a href="#beverages">Beverages</a></li>
                </ul>
            </div>
        </div>
    </header>

    <!-- Hero Section -->
    <section class="hero">
        <div class="container">
            <div class="hero-content">
                <h1>Bold Flavors</h1>
                <p>Experience the intensity of our culinary creations</p>
            </div>
        </div>
    </section>

    <!-- Menu Section -->
    <section class="menu-section">
        <div class="container">
            <div class="section-title">
                <h2>Our Menu</h2>
                <p>Discover our selection of bold and flavorful dishes</p>
            </div>

            <!-- Category Tabs -->
            <div class="category-tabs">
                <button class="category-tab active" data-category="appetizers">Appetizers</button>
                <button class="category-tab" data-category="mains">Main Courses</button>
                <button class="category-tab" data-category="desserts">Desserts</button>
                <button class="category-tab" data-category="beverages">Beverages</button>
            </div>

            <!-- Appetizers Category -->
            <div class="menu-category active" id="appetizers">
                <div class="menu-items">
                    <div class="menu-item">
                        <div class="menu-item-img">
                            <img src="https://images.unsplash.com/photo-1553621042-f6e147245754?ixlib=rb-1.2.1&auto=format&fit=crop&w=500&q=60" alt="Bruschetta">
                        </div>
                        <div class="menu-item-content">
                            <div class="menu-item-header">
                                <h3 class="menu-item-title">Fiery Bruschetta</h3>
                                <span class="menu-item-price">$8.99</span>
                            </div>
                            <p class="menu-item-description">Toasted bread with spicy tomatoes, garlic, and chili flakes</p>
                            <div class="menu-item-footer">
                                <div class="menu-item-rating">
                                    <i class="fas fa-star"></i>
                                    <i class="fas fa-star"></i>
                                    <i class="fas fa-star"></i>
                                    <i class="fas fa-star"></i>
                                    <i class="fas fa-star-half-alt"></i>
                                </div>
                                <button class="add-to-cart">Add to Cart</button>
                            </div>
                        </div>
                    </div>

                    <div class="menu-item">
                        <div class="menu-item-img">
                            <img src="https://images.unsplash.com/photo-1529042410759-befb1204b468?ixlib=rb-1.2.1&auto=format&fit=crop&w=500&q=60" alt="Calamari">
                        </div>
                        <div class="menu-item-content">
                            <div class="menu-item-header">
                                <h3 class="menu-item-title">Spicy Calamari</h3>
                                <span class="menu-item-price">$12.99</span>
                            </div>
                            <p class="menu-item-description">Crispy fried squid rings with sriracha aioli</p>
                            <div class="menu-item-footer">
                                <div class="menu-item-rating">
                                    <i class="fas fa-star"></i>
                                    <i class="fas fa-star"></i>
                                    <i class="fas fa-star"></i>
                                    <i class="fas fa-star"></i>
                                    <i class="far fa-star"></i>
                                </div>
                                <button class="add-to-cart">Add to Cart</button>
                            </div>
                        </div>
                    </div>

                    <div class="menu-item">
                        <div class="menu-item-img">
                            <img src="https://images.unsplash.com/photo-1546069901-ba9599a7e63c?ixlib=rb-1.2.1&auto=format&fit=crop&w=500&q=60" alt="Spinach Artichoke Dip">
                        </div>
                        <div class="menu-item-content">
                            <div class="menu-item-header">
                                <h3 class="menu-item-title">Hot Spinach Dip</h3>
                                <span class="menu-item-price">$10.99</span>
                            </div>
                            <p class="menu-item-description">Creamy dip with jalapeños, spinach, and artichokes</p>
                            <div class="menu-item-footer">
                                <div class="menu-item-rating">
                                    <i class="fas fa-star"></i>
                                    <i class="fas fa-star"></i>
                                    <i class="fas fa-star"></i>
                                    <i class="fas fa-star"></i>
                                    <i class="fas fa-star"></i>
                                </div>
                                <button class="add-to-cart">Add to Cart</button>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Main Courses Category -->
            <div class="menu-category" id="mains">
                <div class="menu-items">
                    <div class="menu-item">
                        <div class="menu-item-img">
                            <img src="https://images.unsplash.com/photo-1568901346375-23c9450c58cd?ixlib=rb-1.2.1&auto=format&fit=crop&w=500&q=60" alt="Grilled Salmon">
                        </div>
                        <div class="menu-item-content">
                            <div class="menu-item-header">
                                <h3 class="menu-item-title">Cajun Salmon</h3>
                                <span class="menu-item-price">$22.99</span>
                            </div>
                            <p class="menu-item-description">Spice-rubbed salmon with fiery lemon butter sauce</p>
                            <div class="menu-item-footer">
                                <div class="menu-item-rating">
                                    <i class="fas fa-star"></i>
                                    <i class="fas fa-star"></i>
                                    <i class="fas fa-star"></i>
                                    <i class="fas fa-star"></i>
                                    <i class="fas fa-star-half-alt"></i>
                                </div>
                                <button class="add-to-cart">Add to Cart</button>
                            </div>
                        </div>
                    </div>

                    <div class="menu-item">
                        <div class="menu-item-img">
                            <img src="https://images.unsplash.com/photo-1565299624946-b28f40a0ca4b?ixlib=rb-1.2.1&auto=format&fit=crop&w=500&q=60" alt="Ribeye Steak">
                        </div>
                        <div class="menu-item-content">
                            <div class="menu-item-header">
                                <h3 class="menu-item-title">Blackened Ribeye</h3>
                                <span class="menu-item-price">$28.99</span>
                            </div>
                            <p class="menu-item-description">12oz ribeye with Cajun spices and garlic mashed potatoes</p>
                            <div class="menu-item-footer">
                                <div class="menu-item-rating">
                                    <i class="fas fa-star"></i>
                                    <i class="fas fa-star"></i>
                                    <i class="fas fa-star"></i>
                                    <i class="fas fa-star"></i>
                                    <i class="fas fa-star"></i>
                                </div>
                                <button class="add-to-cart">Add to Cart</button>
                            </div>
                        </div>
                    </div>

                    <div class="menu-item">
                        <div class="menu-item-img">
                            <img src="https://images.unsplash.com/photo-1567620905732-2d1ec7ab7445?ixlib=rb-1.2.1&auto=format&fit=crop&w=500&q=60" alt="Chicken Parmesan">
                        </div>
                        <div class="menu-item-content">
                            <div class="menu-item-header">
                                <h3 class="menu-item-title">Fire-Roasted Chicken</h3>
                                <span class="menu-item-price">$18.99</span>
                            </div>
                            <p class="menu-item-description">Spice-crusted chicken with arrabbiata sauce</p>
                            <div class="menu-item-footer">
                                <div class="menu-item-rating">
                                    <i class="fas fa-star"></i>
                                    <i class="fas fa-star"></i>
                                    <i class="fas fa-star"></i>
                                    <i class="fas fa-star"></i>
                                    <i class="far fa-star"></i>
                                </div>
                                <button class="add-to-cart">Add to Cart</button>
                            </div>
                        </div>
                    </div>

                    <div class="menu-item">
                        <div class="menu-item-img">
                            <img src="https://images.unsplash.com/photo-1594212699903-ec8a3eca50f5?ixlib=rb-1.2.1&auto=format&fit=crop&w=500&q=60" alt="Vegetable Pasta">
                        </div>
                        <div class="menu-item-content">
                            <div class="menu-item-header">
                                <h3 class="menu-item-title">Arrabbiata Pasta</h3>
                                <span class="menu-item-price">$16.99</span>
                            </div>
                            <p class="menu-item-description">Penne with spicy tomato sauce, chili, and garlic</p>
                            <div class="menu-item-footer">
                                <div class="menu-item-rating">
                                    <i class="fas fa-star"></i>
                                    <i class="fas fa-star"></i>
                                    <i class="fas fa-star"></i>
                                    <i class="fas fa-star"></i>
                                    <i class="fas fa-star-half-alt"></i>
                                </div>
                                <button class="add-to-cart">Add to Cart</button>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Desserts Category -->
            <div class="menu-category" id="desserts">
                <div class="menu-items">
                    <div class="menu-item">
                        <div class="menu-item-img">
                            <img src="https://images.unsplash.com/photo-1563805042-7684c019e7cb?ixlib=rb-1.2.1&auto=format&fit=crop&w=500&q=60" alt="Chocolate Cake">
                        </div>
                        <div class="menu-item-content">
                            <div class="menu-item-header">
                                <h3 class="menu-item-title">Devil's Food Cake</h3>
                                <span class="menu-item-price">$7.99</span>
                            </div>
                            <p class="menu-item-description">Rich chocolate cake with spicy chili ganache</p>
                            <div class="menu-item-footer">
                                <div class="menu-item-rating">
                                    <i class="fas fa-star"></i>
                                    <i class="fas fa-star"></i>
                                    <i class="fas fa-star"></i>
                                    <i class="fas fa-star"></i>
                                    <i class="fas fa-star"></i>
                                </div>
                                <button class="add-to-cart">Add to Cart</button>
                            </div>
                        </div>
                    </div>

                    <div class="menu-item">
                        <div class="menu-item-img">
                            <img src="https://images.unsplash.com/photo-1488477181946-6428a0291777?ixlib=rb-1.2.1&auto=format&fit=crop&w=500&q=60" alt="Tiramisu">
                        </div>
                        <div class="menu-item-content">
                            <div class="menu-item-header">
                                <h3 class="menu-item-title">Red Velvet Tiramisu</h3>
                                <span class="menu-item-price">$8.99</span>
                            </div>
                            <p class="menu-item-description">Layers of red velvet cake with mascarpone</p>
                            <div class="menu-item-footer">
                                <div class="menu-item-rating">
                                    <i class="fas fa-star"></i>
                                    <i class="fas fa-star"></i>
                                    <i class="fas fa-star"></i>
                                    <i class="fas fa-star"></i>
                                    <i class="fas fa-star-half-alt"></i>
                                </div>
                                <button class="add-to-cart">Add to Cart</button>
                            </div>
                        </div>
                    </div>

                    <div class="menu-item">
                        <div class="menu-item-img">
                            <img src="https://images.unsplash.com/photo-1564093497595-593b96d80180?ixlib=rb-1.2.1&auto=format&fit=crop&w=500&q=60" alt="Cheesecake">
                        </div>
                        <div class="menu-item-content">
                            <div class="menu-item-header">
                                <h3 class="menu-item-title">Spiced Cheesecake</h3>
                                <span class="menu-item-price">$7.99</span>
                            </div>
                            <p class="menu-item-description">Cinnamon and cayenne cheesecake with berry compote</p>
                            <div class="menu-item-footer">
                                <div class="menu-item-rating">
                                    <i class="fas fa-star"></i>
                                    <i class="fas fa-star"></i>
                                    <i class="fas fa-star"></i>
                                    <i class="fas fa-star"></i>
                                    <i class="far fa-star"></i>
                                </div>
                                <button class="add-to-cart">Add to Cart</button>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Beverages Category -->
            <div class="menu-category" id="beverages">
                <div class="menu-items">
                    <div class="menu-item">
                        <div class="menu-item-img">
                            <img src="https://images.unsplash.com/photo-1544787219-7f47ccb76574?ixlib=rb-1.2.1&auto=format&fit=crop&w=500&q=60" alt="Fresh Juice">
                        </div>
                        <div class="menu-item-content">
                            <div class="menu-item-header">
                                <h3 class="menu-item-title">Spicy Citrus Juice</h3>
                                <span class="menu-item-price">$4.99</span>
                            </div>
                            <p class="menu-item-description">Fresh orange, lime, and ginger with chili rim</p>
                            <div class="menu-item-footer">
                                <div class="menu-item-rating">
                                    <i class="fas fa-star"></i>
                                    <i class="fas fa-star"></i>
                                    <i class="fas fa-star"></i>
                                    <i class="fas fa-star"></i>
                                    <i class="fas fa-star-half-alt"></i>
                                </div>
                                <button class="add-to-cart">Add to Cart</button>
                            </div>
                        </div>
                    </div>

                    <div class="menu-item">
                        <div class="menu-item-img">
                            <img src="https://images.unsplash.com/photo-1563912438414-f0ba99cb5629?ixlib=rb-1.2.1&auto=format&fit=crop&w=500&q=60" alt="Craft Beer">
                        </div>
                        <div class="menu-item-content">
                            <div class="menu-item-header">
                                <h3 class="menu-item-title">Red Ale</h3>
                                <span class="menu-item-price">$5.99</span>
                            </div>
                            <p class="menu-item-description">Bold and hoppy red ale with caramel notes</p>
                            <div class="menu-item-footer">
                                <div class="menu-item-rating">
                                    <i class="fas fa-star"></i>
                                    <i class="fas fa-star"></i>
                                    <i class="fas fa-star"></i>
                                    <i class="fas fa-star"></i>
                                    <i class="far fa-star"></i>
                                </div>
                                <button class="add-to-cart">Add to Cart</button>
                            </div>
                        </div>
                    </div>

                    <div class="menu-item">
                        <div class="menu-item-img">
                            <img src="https://images.unsplash.com/photo-1576092768241-dec231879fc3?ixlib=rb-1.2.1&auto=format&fit=crop&w=500&q=60" alt="Wine">
                        </div>
                        <div class="menu-item-content">
                            <div class="menu-item-header">
                                <h3 class="menu-item-title">Spicy Red Wine</h3>
                                <span class="menu-item-price">$8.99</span>
                            </div>
                            <p class="menu-item-description">Full-bodied red with hints of pepper and spice</p>
                            <div class="menu-item-footer">
                                <div class="menu-item-rating">
                                    <i class="fas fa-star"></i>
                                    <i class="fas fa-star"></i>
                                    <i class="fas fa-star"></i>
                                    <i class="fas fa-star"></i>
                                    <i class="fas fa-star"></i>
                                </div>
                                <button class="add-to-cart">Add to Cart</button>
                            </div>
                        </div>
                    </div>

                    <div class="menu-item">
                        <div class="menu-item-img">
                            <img src="https://images.unsplash.com/photo-1514228742587-6b1558fcf93a?ixlib=rb-1.2.1&auto=format&fit=crop&w=500&q=60" alt="Cocktails">
                        </div>
                        <div class="menu-item-content">
                            <div class="menu-item-header">
                                <h3 class="menu-item-title">Fireball Cocktail</h3>
                                <span class="menu-item-price">$10.99</span>
                            </div>
                            <p class="menu-item-description">Whiskey, cinnamon, and chili pepper infusion</p>
                            <div class="menu-item-footer">
                                <div class="menu-item-rating">
                                    <i class="fas fa-star"></i>
                                    <i class="fas fa-star"></i>
                                    <i class="fas fa-star"></i>
                                    <i class="fas fa-star"></i>
                                    <i class="fas fa-star-half-alt"></i>
                                </div>
                                <button class="add-to-cart">Add to Cart</button>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </section>

    <!-- Footer -->
    <footer>
        <div class="container">
            <div class="footer-content">
                <div class="footer-section">
                    <h3><i class="fas fa-fire-alt"></i> Flame & Fire</h3>
                    <p>Serving bold, intense flavors since 2015. We believe in pushing culinary boundaries with spice and passion.</p>
                    <div class="social-links">
                        <a href="#"><i class="fab fa-facebook-f"></i></a>
                        <a href="#"><i class="fab fa-twitter"></i></a>
                        <a href="#"><i class="fab fa-instagram"></i></a>
                        <a href="#"><i class="fab fa-youtube"></i></a>
                    </div>
                </div>
                <div class="footer-section">
                    <h3>Hours</h3>
                    <ul>
                        <li>Mon-Fri: 4pm - 11pm</li>
                        <li>Saturday: 12pm - 12am</li>
                        <li>Sunday: 12pm - 10pm</li>
                    </ul>
                </div>
                <div class="footer-section">
                    <h3>Contact</h3>
                    <ul>
                        <li><i class="fas fa-map-marker-alt"></i> 456 Spice Road, Fire District</li>
                        <li><i class="fas fa-phone"></i> (*************</li>
                        <li><i class="fas fa-envelope"></i> <EMAIL></li>
                    </ul>
                </div>
            </div>
            <div class="copyright">
                <p>&copy; 2023 Flame & Fire Restaurant. All Rights Reserved.</p>
            </div>
        </div>
    </footer>

    <script>
        // Mobile menu toggle
        document.querySelector('.mobile-toggle').addEventListener('click', function() {
            document.querySelector('.nav-links').classList.toggle('active');
        });

        // Category tabs functionality
        const categoryTabs = document.querySelectorAll('.category-tab');
        const menuCategories = document.querySelectorAll('.menu-category');

        categoryTabs.forEach(tab => {
            tab.addEventListener('click', () => {
                // Remove active class from all tabs and categories
                categoryTabs.forEach(t => t.classList.remove('active'));
                menuCategories.forEach(c => c.classList.remove('active'));
                
                // Add active class to clicked tab
                tab.classList.add('active');
                
                // Show corresponding category
                const category = tab.getAttribute('data-category');
                document.getElementById(category).classList.add('active');
            });
        });

        // Add to cart functionality
        const addToCartButtons = document.querySelectorAll('.add-to-cart');
        
        addToCartButtons.forEach(button => {
            button.addEventListener('click', function() {
                const itemName = this.closest('.menu-item').querySelector('.menu-item-title').textContent;
                
                // Create a simple notification
                const notification = document.createElement('div');
                notification.style.position = 'fixed';
                notification.style.bottom = '20px';
                notification.style.right = '20px';
                notification.style.backgroundColor = '#D32F2F';
                notification.style.color = 'white';
                notification.style.padding = '15px 20px';
                notification.style.borderRadius = '0';
                notification.style.boxShadow = '0 6px 12px rgba(0,0,0,0.3)';
                notification.style.zIndex = '1000';
                notification.style.transition = 'all 0.3s';
                notification.style.fontWeight = '900';
                notification.style.textTransform = 'uppercase';
                notification.style.letterSpacing = '1px';
                notification.style.borderLeft = '5px solid #B71C1C';
                notification.textContent = `${itemName} added to cart!`;
                
                document.body.appendChild(notification);
                
                // Remove notification after 3 seconds
                setTimeout(() => {
                    notification.style.opacity = '0';
                    setTimeout(() => {
                        document.body.removeChild(notification);
                    }, 300);
                }, 3000);
            });
        });

        // Smooth scrolling for navigation links
        document.querySelectorAll('a[href^="#"]').forEach(anchor => {
            anchor.addEventListener('click', function(e) {
                e.preventDefault();
                
                const targetId = this.getAttribute('href').substring(1);
                if (targetId) {
                    // Find the corresponding tab and click it
                    categoryTabs.forEach(tab => {
                        if (tab.getAttribute('data-category') === targetId) {
                            tab.click();
                        }
                    });
                    
                    // Scroll to the section
                    document.getElementById(targetId).scrollIntoView({
                        behavior: 'smooth',
                        block: 'start'
                    });
                }
                
                // Close mobile menu if open
                document.querySelector('.nav-links').classList.remove('active');
            });
        });
    </script>
</body>
</html>