{"ast": null, "code": "var _jsxFileName = \"C:\\\\restraunat managment system\\\\frontend\\\\src\\\\App.js\",\n  _s = $RefreshSig$();\nimport React, { useState, useEffect } from 'react';\nimport { BrowserRouter as Router, Routes, Route, Navigate } from 'react-router-dom';\nimport CustomerMenu from './components/CustomerMenu';\nimport AdminDashboard from './components/AdminDashboard';\nimport ManagerDashboard from './components/ManagerDashboard';\nimport KitchenDashboard from './components/KitchenDashboard';\nimport Login from './components/Login';\nimport './App.css';\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nfunction App() {\n  _s();\n  const [user, setUser] = useState(null);\n  const [loading, setLoading] = useState(true);\n  useEffect(() => {\n    // Check if user is already logged in\n    const token = localStorage.getItem('token');\n    const savedUser = localStorage.getItem('user');\n    if (token && savedUser) {\n      try {\n        setUser(JSON.parse(savedUser));\n      } catch (error) {\n        console.error('Error parsing saved user:', error);\n        localStorage.removeItem('token');\n        localStorage.removeItem('user');\n      }\n    }\n    setLoading(false);\n  }, []);\n  const handleLogin = userData => {\n    setUser(userData);\n  };\n  const handleLogout = () => {\n    localStorage.removeItem('token');\n    localStorage.removeItem('user');\n    setUser(null);\n  };\n  const DashboardRouter = () => {\n    if (!user) return /*#__PURE__*/_jsxDEV(Navigate, {\n      to: \"/login\",\n      replace: true\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 42,\n      columnNumber: 23\n    }, this);\n    switch (user.role) {\n      case 'admin':\n        return /*#__PURE__*/_jsxDEV(AdminDashboard, {\n          user: user,\n          onLogout: handleLogout\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 46,\n          columnNumber: 16\n        }, this);\n      case 'manager':\n        return /*#__PURE__*/_jsxDEV(ManagerDashboard, {\n          user: user,\n          onLogout: handleLogout\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 48,\n          columnNumber: 16\n        }, this);\n      case 'kitchen_staff':\n        return /*#__PURE__*/_jsxDEV(KitchenDashboard, {\n          user: user,\n          onLogout: handleLogout\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 50,\n          columnNumber: 16\n        }, this);\n      default:\n        return /*#__PURE__*/_jsxDEV(Navigate, {\n          to: \"/login\",\n          replace: true\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 52,\n          columnNumber: 16\n        }, this);\n    }\n  };\n  if (loading) {\n    return /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"loading-screen\",\n      children: [/*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"spinner\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 59,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n        children: \"Loading...\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 60,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 58,\n      columnNumber: 7\n    }, this);\n  }\n  return /*#__PURE__*/_jsxDEV(Router, {\n    future: {\n      v7_startTransition: true,\n      v7_relativeSplatPath: true\n    },\n    children: /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"App\",\n      children: /*#__PURE__*/_jsxDEV(Routes, {\n        children: [/*#__PURE__*/_jsxDEV(Route, {\n          path: \"/table/:tableId\",\n          element: /*#__PURE__*/_jsxDEV(CustomerMenu, {}, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 75,\n            columnNumber: 50\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 75,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(Route, {\n          path: \"/login\",\n          element: user ? /*#__PURE__*/_jsxDEV(Navigate, {\n            to: \"/dashboard\",\n            replace: true\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 80,\n            columnNumber: 29\n          }, this) : /*#__PURE__*/_jsxDEV(Login, {\n            onLogin: handleLogin\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 80,\n            columnNumber: 68\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 78,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(Route, {\n          path: \"/dashboard\",\n          element: /*#__PURE__*/_jsxDEV(DashboardRouter, {}, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 84,\n            columnNumber: 45\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 84,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(Route, {\n          path: \"/admin\",\n          element: /*#__PURE__*/_jsxDEV(Navigate, {\n            to: \"/dashboard\",\n            replace: true\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 87,\n            columnNumber: 41\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 87,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(Route, {\n          path: \"/unauthorized\",\n          element: /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"unauthorized-page\",\n            children: [/*#__PURE__*/_jsxDEV(\"h1\", {\n              children: \"\\uD83D\\uDEAB Access Denied\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 92,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n              children: \"You don't have permission to access this page.\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 93,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n              onClick: handleLogout,\n              children: \"Go to Login\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 94,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 91,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 90,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(Route, {\n          path: \"/\",\n          element: /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"home-page\",\n            children: [/*#__PURE__*/_jsxDEV(\"h1\", {\n              children: \"\\uD83C\\uDF7D\\uFE0F Restaurant QR System\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 101,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n              children: \"Experience seamless dining with our QR code ordering system. Simply scan, order, and enjoy!\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 102,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"cta-buttons\",\n              children: [/*#__PURE__*/_jsxDEV(\"a\", {\n                href: \"/login\",\n                className: \"cta-button cta-primary\",\n                children: \"\\uD83D\\uDC68\\u200D\\uD83D\\uDCBC Staff Login\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 105,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"a\", {\n                href: \"/table/1\",\n                className: \"cta-button cta-secondary\",\n                children: \"\\uD83D\\uDCF1 Try Demo Table\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 108,\n                columnNumber: 17\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 104,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"home-features\",\n              children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"feature-card\",\n                children: [/*#__PURE__*/_jsxDEV(\"span\", {\n                  className: \"feature-icon\",\n                  children: \"\\uD83D\\uDCF1\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 115,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(\"h3\", {\n                  children: \"QR Code Ordering\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 116,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n                  children: \"Customers scan QR codes at their tables to access the digital menu and place orders instantly.\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 117,\n                  columnNumber: 19\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 114,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"feature-card\",\n                children: [/*#__PURE__*/_jsxDEV(\"span\", {\n                  className: \"feature-icon\",\n                  children: \"\\u26A1\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 121,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(\"h3\", {\n                  children: \"Real-time Updates\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 122,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n                  children: \"Orders are updated in real-time with live status tracking from kitchen to table.\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 123,\n                  columnNumber: 19\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 120,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"feature-card\",\n                children: [/*#__PURE__*/_jsxDEV(\"span\", {\n                  className: \"feature-icon\",\n                  children: \"\\uD83D\\uDCB3\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 127,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(\"h3\", {\n                  children: \"Role-based Access\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 128,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n                  children: \"Separate dashboards for admin, manager, and kitchen staff with appropriate permissions.\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 129,\n                  columnNumber: 19\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 126,\n                columnNumber: 17\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 113,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 100,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 99,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 73,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 72,\n      columnNumber: 7\n    }, this)\n  }, void 0, false, {\n    fileName: _jsxFileName,\n    lineNumber: 66,\n    columnNumber: 5\n  }, this);\n}\n_s(App, \"NiO5z6JIqzX62LS5UWDgIqbZYyY=\");\n_c = App;\nexport default App;\nvar _c;\n$RefreshReg$(_c, \"App\");", "map": {"version": 3, "names": ["React", "useState", "useEffect", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "Router", "Routes", "Route", "Navigate", "CustomerMenu", "AdminDashboard", "ManagerDashboard", "KitchenDashboard", "<PERSON><PERSON>", "jsxDEV", "_jsxDEV", "App", "_s", "user", "setUser", "loading", "setLoading", "token", "localStorage", "getItem", "savedUser", "JSON", "parse", "error", "console", "removeItem", "handleLogin", "userData", "handleLogout", "DashboardRouter", "to", "replace", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "role", "onLogout", "className", "children", "future", "v7_startTransition", "v7_relativeSplatPath", "path", "element", "onLogin", "onClick", "href", "_c", "$RefreshReg$"], "sources": ["C:/restraunat managment system/frontend/src/App.js"], "sourcesContent": ["import React, { useState, useEffect } from 'react';\nimport { BrowserRouter as Router, Routes, Route, Navigate } from 'react-router-dom';\nimport CustomerMenu from './components/CustomerMenu';\nimport AdminDashboard from './components/AdminDashboard';\nimport ManagerDashboard from './components/ManagerDashboard';\nimport KitchenDashboard from './components/KitchenDashboard';\nimport Login from './components/Login';\nimport './App.css';\n\nfunction App() {\n  const [user, setUser] = useState(null);\n  const [loading, setLoading] = useState(true);\n\n  useEffect(() => {\n    // Check if user is already logged in\n    const token = localStorage.getItem('token');\n    const savedUser = localStorage.getItem('user');\n\n    if (token && savedUser) {\n      try {\n        setUser(JSON.parse(savedUser));\n      } catch (error) {\n        console.error('Error parsing saved user:', error);\n        localStorage.removeItem('token');\n        localStorage.removeItem('user');\n      }\n    }\n    setLoading(false);\n  }, []);\n\n  const handleLogin = (userData) => {\n    setUser(userData);\n  };\n\n  const handleLogout = () => {\n    localStorage.removeItem('token');\n    localStorage.removeItem('user');\n    setUser(null);\n  };\n\n  const DashboardRouter = () => {\n    if (!user) return <Navigate to=\"/login\" replace />;\n\n    switch (user.role) {\n      case 'admin':\n        return <AdminDashboard user={user} onLogout={handleLogout} />;\n      case 'manager':\n        return <ManagerDashboard user={user} onLogout={handleLogout} />;\n      case 'kitchen_staff':\n        return <KitchenDashboard user={user} onLogout={handleLogout} />;\n      default:\n        return <Navigate to=\"/login\" replace />;\n    }\n  };\n\n  if (loading) {\n    return (\n      <div className=\"loading-screen\">\n        <div className=\"spinner\"></div>\n        <p>Loading...</p>\n      </div>\n    );\n  }\n\n  return (\n    <Router\n      future={{\n        v7_startTransition: true,\n        v7_relativeSplatPath: true\n      }}\n    >\n      <div className=\"App\">\n        <Routes>\n          {/* Customer routes - no authentication required */}\n          <Route path=\"/table/:tableId\" element={<CustomerMenu />} />\n\n          {/* Authentication routes */}\n          <Route\n            path=\"/login\"\n            element={user ? <Navigate to=\"/dashboard\" replace /> : <Login onLogin={handleLogin} />}\n          />\n\n          {/* Dashboard route - redirects based on role */}\n          <Route path=\"/dashboard\" element={<DashboardRouter />} />\n\n          {/* Legacy admin route - redirect to dashboard */}\n          <Route path=\"/admin\" element={<Navigate to=\"/dashboard\" replace />} />\n\n          {/* Unauthorized page */}\n          <Route path=\"/unauthorized\" element={\n            <div className=\"unauthorized-page\">\n              <h1>🚫 Access Denied</h1>\n              <p>You don't have permission to access this page.</p>\n              <button onClick={handleLogout}>Go to Login</button>\n            </div>\n          } />\n\n          {/* Home page */}\n          <Route path=\"/\" element={\n            <div className=\"home-page\">\n              <h1>🍽️ Restaurant QR System</h1>\n              <p>Experience seamless dining with our QR code ordering system. Simply scan, order, and enjoy!</p>\n\n              <div className=\"cta-buttons\">\n                <a href=\"/login\" className=\"cta-button cta-primary\">\n                  👨‍💼 Staff Login\n                </a>\n                <a href=\"/table/1\" className=\"cta-button cta-secondary\">\n                  📱 Try Demo Table\n                </a>\n              </div>\n\n              <div className=\"home-features\">\n                <div className=\"feature-card\">\n                  <span className=\"feature-icon\">📱</span>\n                  <h3>QR Code Ordering</h3>\n                  <p>Customers scan QR codes at their tables to access the digital menu and place orders instantly.</p>\n                </div>\n\n                <div className=\"feature-card\">\n                  <span className=\"feature-icon\">⚡</span>\n                  <h3>Real-time Updates</h3>\n                  <p>Orders are updated in real-time with live status tracking from kitchen to table.</p>\n                </div>\n\n                <div className=\"feature-card\">\n                  <span className=\"feature-icon\">💳</span>\n                  <h3>Role-based Access</h3>\n                  <p>Separate dashboards for admin, manager, and kitchen staff with appropriate permissions.</p>\n                </div>\n              </div>\n            </div>\n          } />\n        </Routes>\n      </div>\n    </Router>\n  );\n}\n\nexport default App;"], "mappings": ";;AAAA,OAAOA,KAAK,IAAIC,QAAQ,EAAEC,SAAS,QAAQ,OAAO;AAClD,SAASC,aAAa,IAAIC,MAAM,EAAEC,MAAM,EAAEC,KAAK,EAAEC,QAAQ,QAAQ,kBAAkB;AACnF,OAAOC,YAAY,MAAM,2BAA2B;AACpD,OAAOC,cAAc,MAAM,6BAA6B;AACxD,OAAOC,gBAAgB,MAAM,+BAA+B;AAC5D,OAAOC,gBAAgB,MAAM,+BAA+B;AAC5D,OAAOC,KAAK,MAAM,oBAAoB;AACtC,OAAO,WAAW;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAEnB,SAASC,GAAGA,CAAA,EAAG;EAAAC,EAAA;EACb,MAAM,CAACC,IAAI,EAAEC,OAAO,CAAC,GAAGjB,QAAQ,CAAC,IAAI,CAAC;EACtC,MAAM,CAACkB,OAAO,EAAEC,UAAU,CAAC,GAAGnB,QAAQ,CAAC,IAAI,CAAC;EAE5CC,SAAS,CAAC,MAAM;IACd;IACA,MAAMmB,KAAK,GAAGC,YAAY,CAACC,OAAO,CAAC,OAAO,CAAC;IAC3C,MAAMC,SAAS,GAAGF,YAAY,CAACC,OAAO,CAAC,MAAM,CAAC;IAE9C,IAAIF,KAAK,IAAIG,SAAS,EAAE;MACtB,IAAI;QACFN,OAAO,CAACO,IAAI,CAACC,KAAK,CAACF,SAAS,CAAC,CAAC;MAChC,CAAC,CAAC,OAAOG,KAAK,EAAE;QACdC,OAAO,CAACD,KAAK,CAAC,2BAA2B,EAAEA,KAAK,CAAC;QACjDL,YAAY,CAACO,UAAU,CAAC,OAAO,CAAC;QAChCP,YAAY,CAACO,UAAU,CAAC,MAAM,CAAC;MACjC;IACF;IACAT,UAAU,CAAC,KAAK,CAAC;EACnB,CAAC,EAAE,EAAE,CAAC;EAEN,MAAMU,WAAW,GAAIC,QAAQ,IAAK;IAChCb,OAAO,CAACa,QAAQ,CAAC;EACnB,CAAC;EAED,MAAMC,YAAY,GAAGA,CAAA,KAAM;IACzBV,YAAY,CAACO,UAAU,CAAC,OAAO,CAAC;IAChCP,YAAY,CAACO,UAAU,CAAC,MAAM,CAAC;IAC/BX,OAAO,CAAC,IAAI,CAAC;EACf,CAAC;EAED,MAAMe,eAAe,GAAGA,CAAA,KAAM;IAC5B,IAAI,CAAChB,IAAI,EAAE,oBAAOH,OAAA,CAACP,QAAQ;MAAC2B,EAAE,EAAC,QAAQ;MAACC,OAAO;IAAA;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAE,CAAC;IAElD,QAAQtB,IAAI,CAACuB,IAAI;MACf,KAAK,OAAO;QACV,oBAAO1B,OAAA,CAACL,cAAc;UAACQ,IAAI,EAAEA,IAAK;UAACwB,QAAQ,EAAET;QAAa;UAAAI,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE,CAAC;MAC/D,KAAK,SAAS;QACZ,oBAAOzB,OAAA,CAACJ,gBAAgB;UAACO,IAAI,EAAEA,IAAK;UAACwB,QAAQ,EAAET;QAAa;UAAAI,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE,CAAC;MACjE,KAAK,eAAe;QAClB,oBAAOzB,OAAA,CAACH,gBAAgB;UAACM,IAAI,EAAEA,IAAK;UAACwB,QAAQ,EAAET;QAAa;UAAAI,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE,CAAC;MACjE;QACE,oBAAOzB,OAAA,CAACP,QAAQ;UAAC2B,EAAE,EAAC,QAAQ;UAACC,OAAO;QAAA;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE,CAAC;IAC3C;EACF,CAAC;EAED,IAAIpB,OAAO,EAAE;IACX,oBACEL,OAAA;MAAK4B,SAAS,EAAC,gBAAgB;MAAAC,QAAA,gBAC7B7B,OAAA;QAAK4B,SAAS,EAAC;MAAS;QAAAN,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAM,CAAC,eAC/BzB,OAAA;QAAA6B,QAAA,EAAG;MAAU;QAAAP,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAG,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACd,CAAC;EAEV;EAEA,oBACEzB,OAAA,CAACV,MAAM;IACLwC,MAAM,EAAE;MACNC,kBAAkB,EAAE,IAAI;MACxBC,oBAAoB,EAAE;IACxB,CAAE;IAAAH,QAAA,eAEF7B,OAAA;MAAK4B,SAAS,EAAC,KAAK;MAAAC,QAAA,eAClB7B,OAAA,CAACT,MAAM;QAAAsC,QAAA,gBAEL7B,OAAA,CAACR,KAAK;UAACyC,IAAI,EAAC,iBAAiB;UAACC,OAAO,eAAElC,OAAA,CAACN,YAAY;YAAA4B,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE;QAAE;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE,CAAC,eAG3DzB,OAAA,CAACR,KAAK;UACJyC,IAAI,EAAC,QAAQ;UACbC,OAAO,EAAE/B,IAAI,gBAAGH,OAAA,CAACP,QAAQ;YAAC2B,EAAE,EAAC,YAAY;YAACC,OAAO;UAAA;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE,CAAC,gBAAGzB,OAAA,CAACF,KAAK;YAACqC,OAAO,EAAEnB;UAAY;YAAAM,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE;QAAE;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACxF,CAAC,eAGFzB,OAAA,CAACR,KAAK;UAACyC,IAAI,EAAC,YAAY;UAACC,OAAO,eAAElC,OAAA,CAACmB,eAAe;YAAAG,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE;QAAE;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE,CAAC,eAGzDzB,OAAA,CAACR,KAAK;UAACyC,IAAI,EAAC,QAAQ;UAACC,OAAO,eAAElC,OAAA,CAACP,QAAQ;YAAC2B,EAAE,EAAC,YAAY;YAACC,OAAO;UAAA;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE;QAAE;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE,CAAC,eAGtEzB,OAAA,CAACR,KAAK;UAACyC,IAAI,EAAC,eAAe;UAACC,OAAO,eACjClC,OAAA;YAAK4B,SAAS,EAAC,mBAAmB;YAAAC,QAAA,gBAChC7B,OAAA;cAAA6B,QAAA,EAAI;YAAgB;cAAAP,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAI,CAAC,eACzBzB,OAAA;cAAA6B,QAAA,EAAG;YAA8C;cAAAP,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAG,CAAC,eACrDzB,OAAA;cAAQoC,OAAO,EAAElB,YAAa;cAAAW,QAAA,EAAC;YAAW;cAAAP,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAQ,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAChD;QACN;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE,CAAC,eAGJzB,OAAA,CAACR,KAAK;UAACyC,IAAI,EAAC,GAAG;UAACC,OAAO,eACrBlC,OAAA;YAAK4B,SAAS,EAAC,WAAW;YAAAC,QAAA,gBACxB7B,OAAA;cAAA6B,QAAA,EAAI;YAAwB;cAAAP,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAI,CAAC,eACjCzB,OAAA;cAAA6B,QAAA,EAAG;YAA2F;cAAAP,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAG,CAAC,eAElGzB,OAAA;cAAK4B,SAAS,EAAC,aAAa;cAAAC,QAAA,gBAC1B7B,OAAA;gBAAGqC,IAAI,EAAC,QAAQ;gBAACT,SAAS,EAAC,wBAAwB;gBAAAC,QAAA,EAAC;cAEpD;gBAAAP,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAG,CAAC,eACJzB,OAAA;gBAAGqC,IAAI,EAAC,UAAU;gBAACT,SAAS,EAAC,0BAA0B;gBAAAC,QAAA,EAAC;cAExD;gBAAAP,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAG,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACD,CAAC,eAENzB,OAAA;cAAK4B,SAAS,EAAC,eAAe;cAAAC,QAAA,gBAC5B7B,OAAA;gBAAK4B,SAAS,EAAC,cAAc;gBAAAC,QAAA,gBAC3B7B,OAAA;kBAAM4B,SAAS,EAAC,cAAc;kBAAAC,QAAA,EAAC;gBAAE;kBAAAP,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAM,CAAC,eACxCzB,OAAA;kBAAA6B,QAAA,EAAI;gBAAgB;kBAAAP,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAI,CAAC,eACzBzB,OAAA;kBAAA6B,QAAA,EAAG;gBAA8F;kBAAAP,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAG,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAClG,CAAC,eAENzB,OAAA;gBAAK4B,SAAS,EAAC,cAAc;gBAAAC,QAAA,gBAC3B7B,OAAA;kBAAM4B,SAAS,EAAC,cAAc;kBAAAC,QAAA,EAAC;gBAAC;kBAAAP,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAM,CAAC,eACvCzB,OAAA;kBAAA6B,QAAA,EAAI;gBAAiB;kBAAAP,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAI,CAAC,eAC1BzB,OAAA;kBAAA6B,QAAA,EAAG;gBAAgF;kBAAAP,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAG,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACpF,CAAC,eAENzB,OAAA;gBAAK4B,SAAS,EAAC,cAAc;gBAAAC,QAAA,gBAC3B7B,OAAA;kBAAM4B,SAAS,EAAC,cAAc;kBAAAC,QAAA,EAAC;gBAAE;kBAAAP,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAM,CAAC,eACxCzB,OAAA;kBAAA6B,QAAA,EAAI;gBAAiB;kBAAAP,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAI,CAAC,eAC1BzB,OAAA;kBAAA6B,QAAA,EAAG;gBAAuF;kBAAAP,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAG,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAC3F,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACH,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACH;QACN;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACE;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACN;EAAC;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACA,CAAC;AAEb;AAACvB,EAAA,CAhIQD,GAAG;AAAAqC,EAAA,GAAHrC,GAAG;AAkIZ,eAAeA,GAAG;AAAC,IAAAqC,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}