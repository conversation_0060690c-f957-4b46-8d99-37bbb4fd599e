{"ast": null, "code": "'use strict';\n\nimport utils from './utils.js';\nimport bind from './helpers/bind.js';\nimport Axios from './core/Axios.js';\nimport mergeConfig from './core/mergeConfig.js';\nimport defaults from './defaults/index.js';\nimport formDataToJSON from './helpers/formDataToJSON.js';\nimport CanceledError from './cancel/CanceledError.js';\nimport CancelToken from './cancel/CancelToken.js';\nimport isCancel from './cancel/isCancel.js';\nimport { VERSION } from './env/data.js';\nimport toFormData from './helpers/toFormData.js';\nimport AxiosError from './core/AxiosError.js';\nimport spread from './helpers/spread.js';\nimport isAxiosError from './helpers/isAxiosError.js';\nimport AxiosHeaders from \"./core/AxiosHeaders.js\";\nimport adapters from './adapters/adapters.js';\nimport HttpStatusCode from './helpers/HttpStatusCode.js';\n\n/**\n * Create an instance of Axios\n *\n * @param {Object} defaultConfig The default config for the instance\n *\n * @returns {Axios} A new instance of Axios\n */\nfunction createInstance(defaultConfig) {\n  const context = new Axios(defaultConfig);\n  const instance = bind(Axios.prototype.request, context);\n\n  // Copy axios.prototype to instance\n  utils.extend(instance, Axios.prototype, context, {\n    allOwnKeys: true\n  });\n\n  // Copy context to instance\n  utils.extend(instance, context, null, {\n    allOwnKeys: true\n  });\n\n  // Factory for creating new instances\n  instance.create = function create(instanceConfig) {\n    return createInstance(mergeConfig(defaultConfig, instanceConfig));\n  };\n  return instance;\n}\n\n// Create the default instance to be exported\nconst axios = createInstance(defaults);\n\n// Expose Axios class to allow class inheritance\naxios.Axios = Axios;\n\n// Expose Cancel & CancelToken\naxios.CanceledError = CanceledError;\naxios.CancelToken = CancelToken;\naxios.isCancel = isCancel;\naxios.VERSION = VERSION;\naxios.toFormData = toFormData;\n\n// Expose AxiosError class\naxios.AxiosError = AxiosError;\n\n// alias for CanceledError for backward compatibility\naxios.Cancel = axios.CanceledError;\n\n// Expose all/spread\naxios.all = function all(promises) {\n  return Promise.all(promises);\n};\naxios.spread = spread;\n\n// Expose isAxiosError\naxios.isAxiosError = isAxiosError;\n\n// Expose mergeConfig\naxios.mergeConfig = mergeConfig;\naxios.AxiosHeaders = AxiosHeaders;\naxios.formToJSON = thing => formDataToJSON(utils.isHTMLForm(thing) ? new FormData(thing) : thing);\naxios.getAdapter = adapters.getAdapter;\naxios.HttpStatusCode = HttpStatusCode;\naxios.default = axios;\n\n// this module should only have a default export\nexport default axios;", "map": {"version": 3, "names": ["utils", "bind", "A<PERSON>os", "mergeConfig", "defaults", "formDataToJSON", "CanceledError", "CancelToken", "isCancel", "VERSION", "toFormData", "AxiosError", "spread", "isAxiosError", "AxiosHeaders", "adapters", "HttpStatusCode", "createInstance", "defaultConfig", "context", "instance", "prototype", "request", "extend", "allOwnKeys", "create", "instanceConfig", "axios", "Cancel", "all", "promises", "Promise", "formToJSON", "thing", "isHTMLForm", "FormData", "getAdapter", "default"], "sources": ["C:/restraunat managment system/frontend/node_modules/axios/lib/axios.js"], "sourcesContent": ["'use strict';\n\nimport utils from './utils.js';\nimport bind from './helpers/bind.js';\nimport Axios from './core/Axios.js';\nimport mergeConfig from './core/mergeConfig.js';\nimport defaults from './defaults/index.js';\nimport formDataToJSON from './helpers/formDataToJSON.js';\nimport CanceledError from './cancel/CanceledError.js';\nimport CancelToken from './cancel/CancelToken.js';\nimport isCancel from './cancel/isCancel.js';\nimport {VERSION} from './env/data.js';\nimport toFormData from './helpers/toFormData.js';\nimport AxiosError from './core/AxiosError.js';\nimport spread from './helpers/spread.js';\nimport isAxiosError from './helpers/isAxiosError.js';\nimport AxiosHeaders from \"./core/AxiosHeaders.js\";\nimport adapters from './adapters/adapters.js';\nimport HttpStatusCode from './helpers/HttpStatusCode.js';\n\n/**\n * Create an instance of Axios\n *\n * @param {Object} defaultConfig The default config for the instance\n *\n * @returns {Axios} A new instance of Axios\n */\nfunction createInstance(defaultConfig) {\n  const context = new Axios(defaultConfig);\n  const instance = bind(Axios.prototype.request, context);\n\n  // Copy axios.prototype to instance\n  utils.extend(instance, Axios.prototype, context, {allOwnKeys: true});\n\n  // Copy context to instance\n  utils.extend(instance, context, null, {allOwnKeys: true});\n\n  // Factory for creating new instances\n  instance.create = function create(instanceConfig) {\n    return createInstance(mergeConfig(defaultConfig, instanceConfig));\n  };\n\n  return instance;\n}\n\n// Create the default instance to be exported\nconst axios = createInstance(defaults);\n\n// Expose Axios class to allow class inheritance\naxios.Axios = Axios;\n\n// Expose Cancel & CancelToken\naxios.CanceledError = CanceledError;\naxios.CancelToken = CancelToken;\naxios.isCancel = isCancel;\naxios.VERSION = VERSION;\naxios.toFormData = toFormData;\n\n// Expose AxiosError class\naxios.AxiosError = AxiosError;\n\n// alias for CanceledError for backward compatibility\naxios.Cancel = axios.CanceledError;\n\n// Expose all/spread\naxios.all = function all(promises) {\n  return Promise.all(promises);\n};\n\naxios.spread = spread;\n\n// Expose isAxiosError\naxios.isAxiosError = isAxiosError;\n\n// Expose mergeConfig\naxios.mergeConfig = mergeConfig;\n\naxios.AxiosHeaders = AxiosHeaders;\n\naxios.formToJSON = thing => formDataToJSON(utils.isHTMLForm(thing) ? new FormData(thing) : thing);\n\naxios.getAdapter = adapters.getAdapter;\n\naxios.HttpStatusCode = HttpStatusCode;\n\naxios.default = axios;\n\n// this module should only have a default export\nexport default axios\n"], "mappings": "AAAA,YAAY;;AAEZ,OAAOA,KAAK,MAAM,YAAY;AAC9B,OAAOC,IAAI,MAAM,mBAAmB;AACpC,OAAOC,KAAK,MAAM,iBAAiB;AACnC,OAAOC,WAAW,MAAM,uBAAuB;AAC/C,OAAOC,QAAQ,MAAM,qBAAqB;AAC1C,OAAOC,cAAc,MAAM,6BAA6B;AACxD,OAAOC,aAAa,MAAM,2BAA2B;AACrD,OAAOC,WAAW,MAAM,yBAAyB;AACjD,OAAOC,QAAQ,MAAM,sBAAsB;AAC3C,SAAQC,OAAO,QAAO,eAAe;AACrC,OAAOC,UAAU,MAAM,yBAAyB;AAChD,OAAOC,UAAU,MAAM,sBAAsB;AAC7C,OAAOC,MAAM,MAAM,qBAAqB;AACxC,OAAOC,YAAY,MAAM,2BAA2B;AACpD,OAAOC,YAAY,MAAM,wBAAwB;AACjD,OAAOC,QAAQ,MAAM,wBAAwB;AAC7C,OAAOC,cAAc,MAAM,6BAA6B;;AAExD;AACA;AACA;AACA;AACA;AACA;AACA;AACA,SAASC,cAAcA,CAACC,aAAa,EAAE;EACrC,MAAMC,OAAO,GAAG,IAAIjB,KAAK,CAACgB,aAAa,CAAC;EACxC,MAAME,QAAQ,GAAGnB,IAAI,CAACC,KAAK,CAACmB,SAAS,CAACC,OAAO,EAAEH,OAAO,CAAC;;EAEvD;EACAnB,KAAK,CAACuB,MAAM,CAACH,QAAQ,EAAElB,KAAK,CAACmB,SAAS,EAAEF,OAAO,EAAE;IAACK,UAAU,EAAE;EAAI,CAAC,CAAC;;EAEpE;EACAxB,KAAK,CAACuB,MAAM,CAACH,QAAQ,EAAED,OAAO,EAAE,IAAI,EAAE;IAACK,UAAU,EAAE;EAAI,CAAC,CAAC;;EAEzD;EACAJ,QAAQ,CAACK,MAAM,GAAG,SAASA,MAAMA,CAACC,cAAc,EAAE;IAChD,OAAOT,cAAc,CAACd,WAAW,CAACe,aAAa,EAAEQ,cAAc,CAAC,CAAC;EACnE,CAAC;EAED,OAAON,QAAQ;AACjB;;AAEA;AACA,MAAMO,KAAK,GAAGV,cAAc,CAACb,QAAQ,CAAC;;AAEtC;AACAuB,KAAK,CAACzB,KAAK,GAAGA,KAAK;;AAEnB;AACAyB,KAAK,CAACrB,aAAa,GAAGA,aAAa;AACnCqB,KAAK,CAACpB,WAAW,GAAGA,WAAW;AAC/BoB,KAAK,CAACnB,QAAQ,GAAGA,QAAQ;AACzBmB,KAAK,CAAClB,OAAO,GAAGA,OAAO;AACvBkB,KAAK,CAACjB,UAAU,GAAGA,UAAU;;AAE7B;AACAiB,KAAK,CAAChB,UAAU,GAAGA,UAAU;;AAE7B;AACAgB,KAAK,CAACC,MAAM,GAAGD,KAAK,CAACrB,aAAa;;AAElC;AACAqB,KAAK,CAACE,GAAG,GAAG,SAASA,GAAGA,CAACC,QAAQ,EAAE;EACjC,OAAOC,OAAO,CAACF,GAAG,CAACC,QAAQ,CAAC;AAC9B,CAAC;AAEDH,KAAK,CAACf,MAAM,GAAGA,MAAM;;AAErB;AACAe,KAAK,CAACd,YAAY,GAAGA,YAAY;;AAEjC;AACAc,KAAK,CAACxB,WAAW,GAAGA,WAAW;AAE/BwB,KAAK,CAACb,YAAY,GAAGA,YAAY;AAEjCa,KAAK,CAACK,UAAU,GAAGC,KAAK,IAAI5B,cAAc,CAACL,KAAK,CAACkC,UAAU,CAACD,KAAK,CAAC,GAAG,IAAIE,QAAQ,CAACF,KAAK,CAAC,GAAGA,KAAK,CAAC;AAEjGN,KAAK,CAACS,UAAU,GAAGrB,QAAQ,CAACqB,UAAU;AAEtCT,KAAK,CAACX,cAAc,GAAGA,cAAc;AAErCW,KAAK,CAACU,OAAO,GAAGV,KAAK;;AAErB;AACA,eAAeA,KAAK", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}