{"ast": null, "code": "var _jsxFileName = \"C:\\\\restraunat managment system\\\\frontend\\\\src\\\\components\\\\AdminDashboard.js\",\n  _s = $RefreshSig$();\nimport React, { useState, useEffect } from 'react';\nimport axios from 'axios';\nimport io from 'socket.io-client';\nimport { jsxDEV as _jsxDEV, Fragment as _Fragment } from \"react/jsx-dev-runtime\";\nconst API_BASE_URL = process.env.REACT_APP_API_BASE_URL;\nconst SOCKET_URL = process.env.REACT_APP_SOCKET_URL;\nconst AdminDashboard = ({\n  user,\n  onLogout\n}) => {\n  _s();\n  const [activeTab, setActiveTab] = useState('orders');\n  const [orders, setOrders] = useState([]);\n  const [tables, setTables] = useState([]);\n  const [menuItems, setMenuItems] = useState([]);\n  const [cashPendingOrders, setCashPendingOrders] = useState([]);\n  const [users, setUsers] = useState([]);\n  const [socket, setSocket] = useState(null);\n  const [notifications, setNotifications] = useState([]);\n  const [loading, setLoading] = useState(true);\n\n  // Menu management states\n  const [editingItem, setEditingItem] = useState(null);\n  const [newItem, setNewItem] = useState({\n    name: '',\n    description: '',\n    price: '',\n    category: 'mains',\n    available: true\n  });\n  const [showAddForm, setShowAddForm] = useState(false);\n\n  // User management states\n  const [editingUser, setEditingUser] = useState(null);\n  const [newUser, setNewUser] = useState({\n    username: '',\n    password: '',\n    name: '',\n    email: '',\n    role: 'kitchen_staff'\n  });\n  const [showAddUserForm, setShowAddUserForm] = useState(false);\n  const getAuthHeaders = () => {\n    const token = localStorage.getItem('token');\n    return {\n      headers: {\n        'Authorization': `Bearer ${token}`\n      }\n    };\n  };\n  useEffect(() => {\n    fetchOrders();\n    fetchTables();\n    fetchMenuItems();\n    fetchCashPendingOrders();\n    fetchUsers();\n    const newSocket = io(SOCKET_URL);\n    setSocket(newSocket);\n    newSocket.on('newOrder', order => {\n      setOrders(prev => [order, ...prev]);\n    });\n    newSocket.on('orderStatusUpdate', updatedOrder => {\n      setOrders(prev => prev.map(order => order._id === updatedOrder._id ? updatedOrder : order));\n    });\n    newSocket.on('tableStatusUpdate', updatedTable => {\n      setTables(prev => prev.map(table => table._id === updatedTable._id ? updatedTable : table));\n    });\n    newSocket.on('cashPaymentPending', order => {\n      setCashPendingOrders(prev => [order, ...prev]);\n      setNotifications(prev => [...prev, {\n        id: Date.now(),\n        type: 'cash_payment',\n        message: `New cash payment order: ${order.orderNumber}`,\n        order\n      }]);\n    });\n    newSocket.on('cashPaymentConfirmed', order => {\n      setCashPendingOrders(prev => prev.filter(o => o._id !== order._id));\n    });\n    return () => newSocket.close();\n  }, []);\n  const fetchOrders = async () => {\n    try {\n      const response = await axios.get(`${API_BASE_URL}/api/admin/orders`, getAuthHeaders());\n      setOrders(response.data);\n      setLoading(false);\n    } catch (error) {\n      var _error$response;\n      console.error('Error fetching orders:', error);\n      if (((_error$response = error.response) === null || _error$response === void 0 ? void 0 : _error$response.status) === 401) {\n        onLogout();\n      }\n      setLoading(false);\n    }\n  };\n  const fetchTables = async () => {\n    try {\n      const response = await axios.get(`${API_BASE_URL}/api/admin/tables`, getAuthHeaders());\n      console.log('Tables fetched:', response.data.length);\n      // Debug: Check if QR codes are present\n      if (response.data.length > 0) {\n        var _firstTable$qrCode;\n        const firstTable = response.data[0];\n        console.log('First table QR code present:', !!firstTable.qrCode);\n        console.log('QR code length:', ((_firstTable$qrCode = firstTable.qrCode) === null || _firstTable$qrCode === void 0 ? void 0 : _firstTable$qrCode.length) || 0);\n      }\n      setTables(response.data);\n    } catch (error) {\n      var _error$response2;\n      console.error('Error fetching tables:', error);\n      if (((_error$response2 = error.response) === null || _error$response2 === void 0 ? void 0 : _error$response2.status) === 401) {\n        onLogout();\n      }\n    }\n  };\n  const fetchMenuItems = async () => {\n    try {\n      const response = await axios.get(`${API_BASE_URL}/api/admin/menu`, getAuthHeaders());\n      setMenuItems(response.data);\n    } catch (error) {\n      var _error$response3;\n      console.error('Error fetching menu items:', error);\n      if (((_error$response3 = error.response) === null || _error$response3 === void 0 ? void 0 : _error$response3.status) === 401) {\n        onLogout();\n      }\n    }\n  };\n  const fetchCashPendingOrders = async () => {\n    try {\n      const response = await axios.get(`${API_BASE_URL}/api/admin/orders/cash-pending`, getAuthHeaders());\n      setCashPendingOrders(response.data);\n    } catch (error) {\n      var _error$response4;\n      console.error('Error fetching cash pending orders:', error);\n      if (((_error$response4 = error.response) === null || _error$response4 === void 0 ? void 0 : _error$response4.status) === 401) {\n        onLogout();\n      }\n    }\n  };\n  const fetchUsers = async () => {\n    try {\n      const response = await axios.get(`${API_BASE_URL}/api/admin/users`, getAuthHeaders());\n      setUsers(response.data);\n    } catch (error) {\n      var _error$response5;\n      console.error('Error fetching users:', error);\n      if (((_error$response5 = error.response) === null || _error$response5 === void 0 ? void 0 : _error$response5.status) === 401) {\n        onLogout();\n      }\n    }\n  };\n  const updateOrderStatus = async (orderId, status) => {\n    try {\n      await axios.patch(`${API_BASE_URL}/api/admin/orders/${orderId}/status`, {\n        status\n      }, getAuthHeaders());\n      fetchOrders();\n    } catch (error) {\n      console.error('Error updating order status:', error);\n      alert('Error updating order status. Please try again.');\n    }\n  };\n  const updateTableStatus = async (tableId, status) => {\n    try {\n      await axios.patch(`${API_BASE_URL}/api/admin/tables/${tableId}/status`, {\n        status\n      }, getAuthHeaders());\n      fetchTables();\n    } catch (error) {\n      console.error('Error updating table status:', error);\n      alert('Error updating table status. Please try again.');\n    }\n  };\n  const regenerateQRCodes = async () => {\n    if (window.confirm('Regenerate QR codes for all tables? This will update all existing QR codes.')) {\n      try {\n        await axios.post(`${API_BASE_URL}/api/admin/tables/regenerate-qr`, {}, getAuthHeaders());\n        alert('QR codes regenerated successfully!');\n        fetchTables();\n      } catch (error) {\n        console.error('Error regenerating QR codes:', error);\n        alert('Error regenerating QR codes. Please try again.');\n      }\n    }\n  };\n  const confirmCashPayment = async orderId => {\n    try {\n      await axios.post(`${API_BASE_URL}/api/orders/${orderId}/payment/cash/confirm`, {}, getAuthHeaders());\n      fetchCashPendingOrders();\n      fetchOrders();\n      fetchTables();\n    } catch (error) {\n      console.error('Error confirming cash payment:', error);\n      alert('Error confirming cash payment. Please try again.');\n    }\n  };\n\n  // Menu management functions\n  const handleAddItem = async e => {\n    e.preventDefault();\n    try {\n      await axios.post(`${API_BASE_URL}/api/admin/menu`, {\n        ...newItem,\n        price: parseFloat(newItem.price)\n      }, getAuthHeaders());\n      setNewItem({\n        name: '',\n        description: '',\n        price: '',\n        category: 'mains',\n        available: true\n      });\n      setShowAddForm(false);\n      fetchMenuItems();\n    } catch (error) {\n      console.error('Error adding menu item:', error);\n      alert('Error adding menu item. Please try again.');\n    }\n  };\n  const handleUpdateItem = async (itemId, updatedData) => {\n    try {\n      await axios.put(`${API_BASE_URL}/api/admin/menu/${itemId}`, updatedData, getAuthHeaders());\n      setEditingItem(null);\n      fetchMenuItems();\n    } catch (error) {\n      console.error('Error updating menu item:', error);\n      alert('Error updating menu item. Please try again.');\n    }\n  };\n  const handleDeleteItem = async itemId => {\n    if (window.confirm('Are you sure you want to delete this menu item?')) {\n      try {\n        await axios.delete(`${API_BASE_URL}/api/admin/menu/${itemId}`, getAuthHeaders());\n        fetchMenuItems();\n      } catch (error) {\n        console.error('Error deleting menu item:', error);\n        alert('Error deleting menu item. Please try again.');\n      }\n    }\n  };\n\n  // User management functions\n  const handleAddUser = async e => {\n    e.preventDefault();\n    try {\n      await axios.post(`${API_BASE_URL}/api/admin/users`, newUser, getAuthHeaders());\n      setNewUser({\n        username: '',\n        password: '',\n        name: '',\n        email: '',\n        role: 'kitchen_staff'\n      });\n      setShowAddUserForm(false);\n      fetchUsers();\n    } catch (error) {\n      console.error('Error adding user:', error);\n      alert('Error adding user. Please try again.');\n    }\n  };\n  const handleUpdateUser = async (userId, updatedData) => {\n    try {\n      await axios.put(`${API_BASE_URL}/api/admin/users/${userId}`, updatedData, getAuthHeaders());\n      setEditingUser(null);\n      fetchUsers();\n    } catch (error) {\n      console.error('Error updating user:', error);\n      alert('Error updating user. Please try again.');\n    }\n  };\n  const handleDeleteUser = async userId => {\n    if (window.confirm('Are you sure you want to delete this user?')) {\n      try {\n        await axios.delete(`${API_BASE_URL}/api/admin/users/${userId}`, getAuthHeaders());\n        fetchUsers();\n      } catch (error) {\n        console.error('Error deleting user:', error);\n        alert('Error deleting user. Please try again.');\n      }\n    }\n  };\n  const renderOrders = () => /*#__PURE__*/_jsxDEV(\"div\", {\n    className: \"orders-section\",\n    children: [/*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"section-header\",\n      children: [/*#__PURE__*/_jsxDEV(\"h2\", {\n        children: [\"\\uD83D\\uDCCB Orders (\", orders.length, \")\"]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 297,\n        columnNumber: 9\n      }, this), orders.length === 0 && /*#__PURE__*/_jsxDEV(\"p\", {\n        style: {\n          color: '#666',\n          fontStyle: 'italic'\n        },\n        children: \"No orders yet today\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 299,\n        columnNumber: 11\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 296,\n      columnNumber: 7\n    }, this), orders.map(order => {\n      var _order$table, _order$totalAmount;\n      return /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"order-card\",\n        children: [/*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"order-header\",\n          children: [/*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"order-info\",\n            children: [/*#__PURE__*/_jsxDEV(\"h3\", {\n              children: [\"\\uD83E\\uDDFE Order #\", order.orderNumber]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 307,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"order-meta\",\n              children: [\"\\uD83D\\uDCCD Table \", (_order$table = order.table) === null || _order$table === void 0 ? void 0 : _order$table.tableNumber, \" \\u2022 \\uD83D\\uDD52 \", new Date(order.createdAt).toLocaleTimeString()]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 308,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 306,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n            className: `status ${order.status}`,\n            children: order.status\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 313,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 305,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"order-items\",\n          children: order.items.map((item, index) => {\n            var _item$menuItem;\n            return /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"order-item\",\n              children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"item-info\",\n                children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"item-name\",\n                  children: ((_item$menuItem = item.menuItem) === null || _item$menuItem === void 0 ? void 0 : _item$menuItem.name) || 'Unknown Item'\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 320,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"item-quantity\",\n                  children: [\"Quantity: \", item.quantity]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 321,\n                  columnNumber: 19\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 319,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"item-price\",\n                children: [\"$\", (item.price * item.quantity).toFixed(2)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 323,\n                columnNumber: 17\n              }, this)]\n            }, index, true, {\n              fileName: _jsxFileName,\n              lineNumber: 318,\n              columnNumber: 15\n            }, this);\n          })\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 316,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"order-total\",\n          children: [\"\\uD83D\\uDCB0 Total: $\", ((_order$totalAmount = order.totalAmount) === null || _order$totalAmount === void 0 ? void 0 : _order$totalAmount.toFixed(2)) || '0.00']\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 328,\n          columnNumber: 11\n        }, this), order.customerName && /*#__PURE__*/_jsxDEV(\"div\", {\n          style: {\n            marginTop: '15px',\n            padding: '10px',\n            background: '#f8f9fa',\n            borderRadius: '8px'\n          },\n          children: [/*#__PURE__*/_jsxDEV(\"strong\", {\n            children: \"\\uD83D\\uDC64 Customer:\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 334,\n            columnNumber: 15\n          }, this), \" \", order.customerName]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 333,\n          columnNumber: 13\n        }, this), order.specialInstructions && order.specialInstructions.trim() !== '' ? /*#__PURE__*/_jsxDEV(\"div\", {\n          style: {\n            marginTop: '10px',\n            padding: '12px',\n            background: '#fff3cd',\n            borderRadius: '8px',\n            border: '2px solid #ffc107',\n            fontSize: '0.95rem'\n          },\n          children: [/*#__PURE__*/_jsxDEV(\"strong\", {\n            children: \"\\uD83D\\uDCDD Special Instructions:\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 347,\n            columnNumber: 15\n          }, this), \" \", order.specialInstructions]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 339,\n          columnNumber: 13\n        }, this) : /*#__PURE__*/_jsxDEV(\"div\", {\n          style: {\n            marginTop: '10px',\n            padding: '8px',\n            background: '#f8f9fa',\n            borderRadius: '6px',\n            color: '#6c757d',\n            fontSize: '0.9rem',\n            fontStyle: 'italic'\n          },\n          children: \"\\uD83D\\uDCDD No special instructions\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 350,\n          columnNumber: 13\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"status-buttons\",\n          children: [/*#__PURE__*/_jsxDEV(\"button\", {\n            className: \"btn-confirm\",\n            onClick: () => updateOrderStatus(order._id, 'confirmed'),\n            disabled: order.status !== 'pending',\n            children: \"\\u2705 Confirm\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 364,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n            className: \"btn-prepare\",\n            onClick: () => updateOrderStatus(order._id, 'preparing'),\n            disabled: order.status !== 'confirmed',\n            children: \"\\uD83D\\uDC68\\u200D\\uD83C\\uDF73 Preparing\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 371,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n            className: \"btn-ready\",\n            onClick: () => updateOrderStatus(order._id, 'ready'),\n            disabled: order.status !== 'preparing',\n            children: \"\\uD83D\\uDD14 Ready\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 378,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n            className: \"btn-serve\",\n            onClick: () => updateOrderStatus(order._id, 'served'),\n            disabled: order.status !== 'ready',\n            children: \"\\uD83C\\uDF7D\\uFE0F Served\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 385,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 363,\n          columnNumber: 11\n        }, this)]\n      }, order._id, true, {\n        fileName: _jsxFileName,\n        lineNumber: 304,\n        columnNumber: 9\n      }, this);\n    })]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 295,\n    columnNumber: 5\n  }, this);\n  const renderTables = () => {\n    const availableTables = tables.filter(t => t.status === 'available').length;\n    const occupiedTables = tables.filter(t => t.status === 'occupied').length;\n    const reservedTables = tables.filter(t => t.status === 'reserved').length;\n    return /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"tables-section\",\n      children: [/*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"section-header\",\n        children: [/*#__PURE__*/_jsxDEV(\"h2\", {\n          children: [\"\\uD83E\\uDE91 Tables (\", tables.length, \")\"]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 406,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          children: [/*#__PURE__*/_jsxDEV(\"p\", {\n            style: {\n              color: '#666',\n              margin: '0 0 10px 0'\n            },\n            children: \"Real-time table management with automatic status updates\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 408,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n            className: \"btn-add-item\",\n            onClick: regenerateQRCodes,\n            style: {\n              fontSize: '0.9rem',\n              padding: '8px 16px'\n            },\n            children: \"\\uD83D\\uDD04 Regenerate QR Codes\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 409,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 407,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 405,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        style: {\n          display: 'grid',\n          gridTemplateColumns: 'repeat(auto-fit, minmax(200px, 1fr))',\n          gap: '20px',\n          marginBottom: '30px'\n        },\n        children: [/*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"card\",\n          style: {\n            textAlign: 'center',\n            background: '#d4edda'\n          },\n          children: [/*#__PURE__*/_jsxDEV(\"h3\", {\n            style: {\n              color: '#155724',\n              margin: '0 0 10px 0'\n            },\n            children: \"\\u2705 Available\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 427,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            style: {\n              fontSize: '2rem',\n              fontWeight: 'bold',\n              color: '#155724'\n            },\n            children: availableTables\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 428,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 426,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"card\",\n          style: {\n            textAlign: 'center',\n            background: '#f8d7da'\n          },\n          children: [/*#__PURE__*/_jsxDEV(\"h3\", {\n            style: {\n              color: '#721c24',\n              margin: '0 0 10px 0'\n            },\n            children: \"\\uD83D\\uDD34 Occupied\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 431,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            style: {\n              fontSize: '2rem',\n              fontWeight: 'bold',\n              color: '#721c24'\n            },\n            children: occupiedTables\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 432,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 430,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"card\",\n          style: {\n            textAlign: 'center',\n            background: '#fff3cd'\n          },\n          children: [/*#__PURE__*/_jsxDEV(\"h3\", {\n            style: {\n              color: '#856404',\n              margin: '0 0 10px 0'\n            },\n            children: \"\\uD83D\\uDFE1 Reserved\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 435,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            style: {\n              fontSize: '2rem',\n              fontWeight: 'bold',\n              color: '#856404'\n            },\n            children: reservedTables\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 436,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 434,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 420,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"tables-grid\",\n        children: tables.map(table => /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"table-card\",\n          children: [/*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"table-number\",\n            children: [\"#\", table.tableNumber]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 443,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"table-capacity\",\n            children: [\"\\uD83D\\uDC65 Capacity: \", table.capacity, \" people\"]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 444,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"table-status\",\n            children: /*#__PURE__*/_jsxDEV(\"span\", {\n              className: `status ${table.status}`,\n              children: [table.status === 'available' && '✅ Available', table.status === 'occupied' && '🔴 Occupied', table.status === 'reserved' && '🟡 Reserved']\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 448,\n              columnNumber: 15\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 447,\n            columnNumber: 13\n          }, this), table.status === 'occupied' && /*#__PURE__*/_jsxDEV(\"div\", {\n            style: {\n              background: '#fff3cd',\n              padding: '8px',\n              borderRadius: '8px',\n              marginTop: '10px',\n              fontSize: '0.85rem',\n              color: '#856404'\n            },\n            children: \"\\uD83C\\uDF7D\\uFE0F Currently serving customers\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 456,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"table-actions\",\n            children: [/*#__PURE__*/_jsxDEV(\"button\", {\n              className: \"btn-available\",\n              onClick: () => updateTableStatus(table._id, 'available'),\n              children: \"\\u2705 Available\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 469,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n              className: \"btn-occupied\",\n              onClick: () => updateTableStatus(table._id, 'occupied'),\n              children: \"\\uD83D\\uDD34 Occupied\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 475,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n              className: \"btn-reserved\",\n              onClick: () => updateTableStatus(table._id, 'reserved'),\n              children: \"\\uD83D\\uDFE1 Reserved\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 481,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 468,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"qr-code\",\n            children: table.qrCode ? /*#__PURE__*/_jsxDEV(_Fragment, {\n              children: [/*#__PURE__*/_jsxDEV(\"img\", {\n                src: table.qrCode,\n                alt: `QR Code for Table ${table.tableNumber}`,\n                onError: e => {\n                  console.error('QR Code image failed to load for table', table.tableNumber);\n                  e.target.style.display = 'none';\n                  e.target.nextSibling.style.display = 'block';\n                }\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 492,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                style: {\n                  display: 'none',\n                  color: '#e74c3c',\n                  fontSize: '0.9rem'\n                },\n                children: \"\\u274C QR Code failed to load\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 501,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n                children: \"\\uD83D\\uDCF1 Scan to order\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 504,\n                columnNumber: 19\n              }, this)]\n            }, void 0, true) : /*#__PURE__*/_jsxDEV(\"div\", {\n              style: {\n                color: '#e74c3c',\n                fontSize: '0.9rem'\n              },\n              children: [\"\\u274C No QR Code available\", /*#__PURE__*/_jsxDEV(\"br\", {}, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 509,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(\"small\", {\n                children: [\"URL: \", `${window.location.origin}/table/${table.tableNumber}`]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 510,\n                columnNumber: 19\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 507,\n              columnNumber: 17\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 489,\n            columnNumber: 13\n          }, this)]\n        }, table._id, true, {\n          fileName: _jsxFileName,\n          lineNumber: 442,\n          columnNumber: 11\n        }, this))\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 440,\n        columnNumber: 7\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 404,\n      columnNumber: 7\n    }, this);\n  };\n  const renderMenuManagement = () => {\n    const groupedItems = menuItems.reduce((acc, item) => {\n      if (!acc[item.category]) acc[item.category] = [];\n      acc[item.category].push(item);\n      return acc;\n    }, {});\n    return /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"menu-management-section\",\n      children: [/*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"section-header\",\n        children: [/*#__PURE__*/_jsxDEV(\"h2\", {\n          children: [\"\\uD83D\\uDCCB Menu Management (\", menuItems.length, \" items)\"]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 531,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n          className: \"btn-add-item\",\n          onClick: () => setShowAddForm(!showAddForm),\n          children: showAddForm ? '❌ Cancel' : '➕ Add New Item'\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 532,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 530,\n        columnNumber: 9\n      }, this), showAddForm && /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"add-item-form\",\n        children: [/*#__PURE__*/_jsxDEV(\"h3\", {\n          children: \"Add New Menu Item\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 542,\n          columnNumber: 13\n        }, this), /*#__PURE__*/_jsxDEV(\"form\", {\n          onSubmit: handleAddItem,\n          children: [/*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"form-row\",\n            children: [/*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"form-group\",\n              children: [/*#__PURE__*/_jsxDEV(\"label\", {\n                children: \"Name *\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 546,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(\"input\", {\n                type: \"text\",\n                value: newItem.name,\n                onChange: e => setNewItem({\n                  ...newItem,\n                  name: e.target.value\n                }),\n                required: true\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 547,\n                columnNumber: 19\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 545,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"form-group\",\n              children: [/*#__PURE__*/_jsxDEV(\"label\", {\n                children: \"Price *\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 555,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(\"input\", {\n                type: \"number\",\n                step: \"0.01\",\n                value: newItem.price,\n                onChange: e => setNewItem({\n                  ...newItem,\n                  price: e.target.value\n                }),\n                required: true\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 556,\n                columnNumber: 19\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 554,\n              columnNumber: 17\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 544,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"form-group\",\n            children: [/*#__PURE__*/_jsxDEV(\"label\", {\n              children: \"Category *\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 566,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(\"select\", {\n              value: newItem.category,\n              onChange: e => setNewItem({\n                ...newItem,\n                category: e.target.value\n              }),\n              children: [/*#__PURE__*/_jsxDEV(\"option\", {\n                value: \"appetizers\",\n                children: \"Appetizers\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 571,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(\"option\", {\n                value: \"mains\",\n                children: \"Main Courses\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 572,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(\"option\", {\n                value: \"desserts\",\n                children: \"Desserts\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 573,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(\"option\", {\n                value: \"beverages\",\n                children: \"Beverages\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 574,\n                columnNumber: 19\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 567,\n              columnNumber: 17\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 565,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"form-group\",\n            children: [/*#__PURE__*/_jsxDEV(\"label\", {\n              children: \"Description\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 578,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(\"textarea\", {\n              value: newItem.description,\n              onChange: e => setNewItem({\n                ...newItem,\n                description: e.target.value\n              }),\n              rows: \"3\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 579,\n              columnNumber: 17\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 577,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"form-actions\",\n            children: [/*#__PURE__*/_jsxDEV(\"button\", {\n              type: \"submit\",\n              className: \"btn-save\",\n              children: \"Save Item\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 586,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n              type: \"button\",\n              onClick: () => setShowAddForm(false),\n              className: \"btn-cancel\",\n              children: \"Cancel\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 587,\n              columnNumber: 17\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 585,\n            columnNumber: 15\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 543,\n          columnNumber: 13\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 541,\n        columnNumber: 11\n      }, this), Object.keys(groupedItems).map(category => /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"menu-category-section\",\n        children: [/*#__PURE__*/_jsxDEV(\"h3\", {\n          className: \"category-title\",\n          children: [category === 'appetizers' && '🥗', category === 'mains' && '🍽️', category === 'desserts' && '🍰', category === 'beverages' && '🥤', category.charAt(0).toUpperCase() + category.slice(1), \" (\", groupedItems[category].length, \")\"]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 595,\n          columnNumber: 13\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"menu-items-grid\",\n          children: groupedItems[category].map(item => /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"menu-item-card\",\n            children: editingItem === item._id ? /*#__PURE__*/_jsxDEV(EditItemForm, {\n              item: item,\n              onSave: data => handleUpdateItem(item._id, data),\n              onCancel: () => setEditingItem(null)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 607,\n              columnNumber: 21\n            }, this) : /*#__PURE__*/_jsxDEV(_Fragment, {\n              children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"item-header\",\n                children: [/*#__PURE__*/_jsxDEV(\"h4\", {\n                  children: item.name\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 615,\n                  columnNumber: 25\n                }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                  className: \"item-price\",\n                  children: [\"$\", item.price.toFixed(2)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 616,\n                  columnNumber: 25\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 614,\n                columnNumber: 23\n              }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n                className: \"item-description\",\n                children: item.description\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 618,\n                columnNumber: 23\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"item-status\",\n                children: /*#__PURE__*/_jsxDEV(\"span\", {\n                  className: `availability ${item.available ? 'available' : 'unavailable'}`,\n                  children: item.available ? '✅ Available' : '❌ Unavailable'\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 620,\n                  columnNumber: 25\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 619,\n                columnNumber: 23\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"item-actions\",\n                children: [/*#__PURE__*/_jsxDEV(\"button\", {\n                  className: \"btn-edit\",\n                  onClick: () => setEditingItem(item._id),\n                  children: \"\\u270F\\uFE0F Edit\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 625,\n                  columnNumber: 25\n                }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n                  className: \"btn-delete\",\n                  onClick: () => handleDeleteItem(item._id),\n                  children: \"\\uD83D\\uDDD1\\uFE0F Delete\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 631,\n                  columnNumber: 25\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 624,\n                columnNumber: 23\n              }, this)]\n            }, void 0, true)\n          }, item._id, false, {\n            fileName: _jsxFileName,\n            lineNumber: 605,\n            columnNumber: 17\n          }, this))\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 603,\n          columnNumber: 13\n        }, this)]\n      }, category, true, {\n        fileName: _jsxFileName,\n        lineNumber: 594,\n        columnNumber: 11\n      }, this))]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 529,\n      columnNumber: 7\n    }, this);\n  };\n\n  // EditItemForm component\n  const EditItemForm = ({\n    item,\n    onSave,\n    onCancel\n  }) => {\n    const handleSubmit = e => {\n      e.preventDefault();\n      const formData = new FormData(e.target);\n      const updatedData = {\n        name: formData.get('name'),\n        description: formData.get('description'),\n        price: parseFloat(formData.get('price')),\n        category: formData.get('category'),\n        available: formData.get('available') === 'on'\n      };\n      onSave(updatedData);\n    };\n    return /*#__PURE__*/_jsxDEV(\"form\", {\n      onSubmit: handleSubmit,\n      className: \"edit-item-form\",\n      children: [/*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"form-group\",\n        children: /*#__PURE__*/_jsxDEV(\"input\", {\n          type: \"text\",\n          name: \"name\",\n          defaultValue: item.name,\n          required: true,\n          placeholder: \"Item name\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 667,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 666,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"form-group\",\n        children: /*#__PURE__*/_jsxDEV(\"input\", {\n          type: \"number\",\n          name: \"price\",\n          step: \"0.01\",\n          defaultValue: item.price,\n          required: true,\n          placeholder: \"Price\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 676,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 675,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"form-group\",\n        children: /*#__PURE__*/_jsxDEV(\"select\", {\n          name: \"category\",\n          defaultValue: item.category,\n          children: [/*#__PURE__*/_jsxDEV(\"option\", {\n            value: \"appetizers\",\n            children: \"Appetizers\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 687,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"option\", {\n            value: \"mains\",\n            children: \"Main Courses\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 688,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"option\", {\n            value: \"desserts\",\n            children: \"Desserts\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 689,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"option\", {\n            value: \"beverages\",\n            children: \"Beverages\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 690,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 686,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 685,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"form-group\",\n        children: /*#__PURE__*/_jsxDEV(\"textarea\", {\n          name: \"description\",\n          defaultValue: item.description,\n          rows: \"2\",\n          placeholder: \"Description\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 694,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 693,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"form-group\",\n        children: /*#__PURE__*/_jsxDEV(\"label\", {\n          children: [/*#__PURE__*/_jsxDEV(\"input\", {\n            type: \"checkbox\",\n            name: \"available\",\n            defaultChecked: item.available\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 703,\n            columnNumber: 13\n          }, this), \"Available\"]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 702,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 701,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"form-actions\",\n        children: [/*#__PURE__*/_jsxDEV(\"button\", {\n          type: \"submit\",\n          className: \"btn-save\",\n          children: \"Save\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 712,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n          type: \"button\",\n          onClick: onCancel,\n          className: \"btn-cancel\",\n          children: \"Cancel\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 713,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 711,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 665,\n      columnNumber: 7\n    }, this);\n  };\n  const renderUserManagement = () => /*#__PURE__*/_jsxDEV(\"div\", {\n    className: \"user-management-section\",\n    children: [/*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"section-header\",\n      children: [/*#__PURE__*/_jsxDEV(\"h2\", {\n        children: [\"\\uD83D\\uDC65 User Management (\", users.length, \" users)\"]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 722,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n        className: \"btn-add-item\",\n        onClick: () => setShowAddUserForm(!showAddUserForm),\n        children: showAddUserForm ? '❌ Cancel' : '➕ Add New User'\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 723,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 721,\n      columnNumber: 7\n    }, this), showAddUserForm && /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"add-item-form\",\n      children: [/*#__PURE__*/_jsxDEV(\"h3\", {\n        children: \"Add New User\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 733,\n        columnNumber: 11\n      }, this), /*#__PURE__*/_jsxDEV(\"form\", {\n        onSubmit: handleAddUser,\n        children: [/*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"form-row\",\n          children: [/*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"form-group\",\n            children: [/*#__PURE__*/_jsxDEV(\"label\", {\n              children: \"Username *\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 737,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(\"input\", {\n              type: \"text\",\n              value: newUser.username,\n              onChange: e => setNewUser({\n                ...newUser,\n                username: e.target.value\n              }),\n              required: true\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 738,\n              columnNumber: 17\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 736,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"form-group\",\n            children: [/*#__PURE__*/_jsxDEV(\"label\", {\n              children: \"Password *\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 746,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(\"input\", {\n              type: \"password\",\n              value: newUser.password,\n              onChange: e => setNewUser({\n                ...newUser,\n                password: e.target.value\n              }),\n              required: true\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 747,\n              columnNumber: 17\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 745,\n            columnNumber: 15\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 735,\n          columnNumber: 13\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"form-row\",\n          children: [/*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"form-group\",\n            children: [/*#__PURE__*/_jsxDEV(\"label\", {\n              children: \"Full Name *\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 757,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(\"input\", {\n              type: \"text\",\n              value: newUser.name,\n              onChange: e => setNewUser({\n                ...newUser,\n                name: e.target.value\n              }),\n              required: true\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 758,\n              columnNumber: 17\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 756,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"form-group\",\n            children: [/*#__PURE__*/_jsxDEV(\"label\", {\n              children: \"Email *\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 766,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(\"input\", {\n              type: \"email\",\n              value: newUser.email,\n              onChange: e => setNewUser({\n                ...newUser,\n                email: e.target.value\n              }),\n              required: true\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 767,\n              columnNumber: 17\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 765,\n            columnNumber: 15\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 755,\n          columnNumber: 13\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"form-group\",\n          children: [/*#__PURE__*/_jsxDEV(\"label\", {\n            children: \"Role *\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 776,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(\"select\", {\n            value: newUser.role,\n            onChange: e => setNewUser({\n              ...newUser,\n              role: e.target.value\n            }),\n            children: [/*#__PURE__*/_jsxDEV(\"option\", {\n              value: \"kitchen_staff\",\n              children: \"Kitchen Staff\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 781,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(\"option\", {\n              value: \"manager\",\n              children: \"Manager\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 782,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(\"option\", {\n              value: \"admin\",\n              children: \"Admin\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 783,\n              columnNumber: 17\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 777,\n            columnNumber: 15\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 775,\n          columnNumber: 13\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"form-actions\",\n          children: [/*#__PURE__*/_jsxDEV(\"button\", {\n            type: \"submit\",\n            className: \"btn-save\",\n            children: \"Create User\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 787,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n            type: \"button\",\n            onClick: () => setShowAddUserForm(false),\n            className: \"btn-cancel\",\n            children: \"Cancel\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 788,\n            columnNumber: 15\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 786,\n          columnNumber: 13\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 734,\n        columnNumber: 11\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 732,\n      columnNumber: 9\n    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"users-grid\",\n      children: users.map(userItem => /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"user-card\",\n        children: [/*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"user-header\",\n          children: [/*#__PURE__*/_jsxDEV(\"h4\", {\n            children: userItem.name\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 798,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n            className: `user-role ${userItem.role}`,\n            children: [userItem.role === 'admin' && '👨‍💻 Admin', userItem.role === 'manager' && '👨‍💼 Manager', userItem.role === 'kitchen_staff' && '👨‍🍳 Kitchen Staff']\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 799,\n            columnNumber: 15\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 797,\n          columnNumber: 13\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"user-details\",\n          children: [/*#__PURE__*/_jsxDEV(\"p\", {\n            children: [/*#__PURE__*/_jsxDEV(\"strong\", {\n              children: \"Username:\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 806,\n              columnNumber: 18\n            }, this), \" \", userItem.username]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 806,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n            children: [/*#__PURE__*/_jsxDEV(\"strong\", {\n              children: \"Email:\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 807,\n              columnNumber: 18\n            }, this), \" \", userItem.email]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 807,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n            children: [/*#__PURE__*/_jsxDEV(\"strong\", {\n              children: \"Status:\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 808,\n              columnNumber: 18\n            }, this), \" \", userItem.isActive ? '✅ Active' : '❌ Inactive']\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 808,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n            children: [/*#__PURE__*/_jsxDEV(\"strong\", {\n              children: \"Created:\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 809,\n              columnNumber: 18\n            }, this), \" \", new Date(userItem.createdAt).toLocaleDateString()]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 809,\n            columnNumber: 15\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 805,\n          columnNumber: 13\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"user-actions\",\n          children: [/*#__PURE__*/_jsxDEV(\"button\", {\n            className: \"btn-edit\",\n            onClick: () => setEditingUser(userItem._id),\n            children: \"\\u270F\\uFE0F Edit\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 812,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n            className: \"btn-delete\",\n            onClick: () => handleDeleteUser(userItem._id),\n            disabled: userItem._id === (user === null || user === void 0 ? void 0 : user._id),\n            children: \"\\uD83D\\uDDD1\\uFE0F Delete\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 818,\n            columnNumber: 15\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 811,\n          columnNumber: 13\n        }, this)]\n      }, userItem._id, true, {\n        fileName: _jsxFileName,\n        lineNumber: 796,\n        columnNumber: 11\n      }, this))\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 794,\n      columnNumber: 7\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 720,\n    columnNumber: 5\n  }, this);\n  const renderCashPayments = () => /*#__PURE__*/_jsxDEV(\"div\", {\n    className: \"cash-payments-section\",\n    children: [/*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"section-header\",\n      children: [/*#__PURE__*/_jsxDEV(\"h2\", {\n        children: [\"\\uD83D\\uDCB0 Cash Payment Confirmations (\", cashPendingOrders.length, \")\"]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 835,\n        columnNumber: 9\n      }, this), cashPendingOrders.length === 0 && /*#__PURE__*/_jsxDEV(\"p\", {\n        style: {\n          color: '#666',\n          fontStyle: 'italic'\n        },\n        children: \"No pending cash payments\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 837,\n        columnNumber: 11\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 834,\n      columnNumber: 7\n    }, this), cashPendingOrders.map(order => /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"cash-payment-card\",\n      children: [/*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"order-header\",\n        children: [/*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"order-info\",\n          children: [/*#__PURE__*/_jsxDEV(\"h3\", {\n            children: [\"Order #\", order.orderNumber]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 845,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"order-meta\",\n            children: [/*#__PURE__*/_jsxDEV(\"span\", {\n              className: \"table-info\",\n              children: [\"\\uD83D\\uDCCD Table \", order.table.tableNumber]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 847,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n              className: \"order-time\",\n              children: [\"\\uD83D\\uDD52 \", new Date(order.createdAt).toLocaleTimeString()]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 848,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n              className: \"payment-method\",\n              children: \"\\uD83D\\uDCB0 Cash Payment\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 849,\n              columnNumber: 17\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 846,\n            columnNumber: 15\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 844,\n          columnNumber: 13\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"order-total\",\n          children: /*#__PURE__*/_jsxDEV(\"strong\", {\n            children: [\"$\", order.totalAmount.toFixed(2)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 853,\n            columnNumber: 15\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 852,\n          columnNumber: 13\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 843,\n        columnNumber: 11\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"customer-details\",\n        children: [/*#__PURE__*/_jsxDEV(\"p\", {\n          children: [/*#__PURE__*/_jsxDEV(\"strong\", {\n            children: \"Customer:\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 858,\n            columnNumber: 16\n          }, this), \" \", order.customerName]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 858,\n          columnNumber: 13\n        }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n          children: [/*#__PURE__*/_jsxDEV(\"strong\", {\n            children: \"Phone:\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 859,\n            columnNumber: 16\n          }, this), \" \", order.customerPhone]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 859,\n          columnNumber: 13\n        }, this), order.specialInstructions && /*#__PURE__*/_jsxDEV(\"p\", {\n          children: [/*#__PURE__*/_jsxDEV(\"strong\", {\n            children: \"Special Instructions:\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 861,\n            columnNumber: 18\n          }, this), \" \", order.specialInstructions]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 861,\n          columnNumber: 15\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 857,\n        columnNumber: 11\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"order-items\",\n        children: [/*#__PURE__*/_jsxDEV(\"h4\", {\n          children: \"Items:\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 866,\n          columnNumber: 13\n        }, this), order.items.map((item, index) => /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"order-item\",\n          children: [/*#__PURE__*/_jsxDEV(\"span\", {\n            children: [item.quantity, \"x \", item.menuItem.name]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 869,\n            columnNumber: 17\n          }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n            children: [\"$\", (item.price * item.quantity).toFixed(2)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 870,\n            columnNumber: 17\n          }, this)]\n        }, index, true, {\n          fileName: _jsxFileName,\n          lineNumber: 868,\n          columnNumber: 15\n        }, this))]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 865,\n        columnNumber: 11\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"cash-payment-actions\",\n        children: /*#__PURE__*/_jsxDEV(\"button\", {\n          className: \"btn-confirm-cash\",\n          onClick: () => confirmCashPayment(order._id),\n          children: \"\\u2705 Confirm Cash Payment\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 876,\n          columnNumber: 13\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 875,\n        columnNumber: 11\n      }, this)]\n    }, order._id, true, {\n      fileName: _jsxFileName,\n      lineNumber: 842,\n      columnNumber: 9\n    }, this))]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 833,\n    columnNumber: 5\n  }, this);\n  return /*#__PURE__*/_jsxDEV(\"div\", {\n    className: \"admin-dashboard\",\n    children: [/*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"admin-header\",\n      children: [/*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"header-left\",\n        children: [/*#__PURE__*/_jsxDEV(\"h1\", {\n          children: \"\\uD83D\\uDC68\\u200D\\uD83D\\uDCBC Admin Dashboard\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 892,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n          children: [\"Welcome, \", (user === null || user === void 0 ? void 0 : user.name) || 'Administrator']\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 893,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 891,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"header-right\",\n        children: [/*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"notifications\",\n          children: notifications.map(notification => /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"notification\",\n            children: notification.message\n          }, notification.id, false, {\n            fileName: _jsxFileName,\n            lineNumber: 898,\n            columnNumber: 15\n          }, this))\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 896,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n          className: \"logout-btn\",\n          onClick: onLogout,\n          children: \"\\uD83D\\uDEAA Logout\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 903,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 895,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 890,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"admin-tabs\",\n      children: [/*#__PURE__*/_jsxDEV(\"button\", {\n        className: activeTab === 'orders' ? 'active' : '',\n        onClick: () => setActiveTab('orders'),\n        children: \"Orders\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 910,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n        className: activeTab === 'cash-payments' ? 'active' : '',\n        onClick: () => setActiveTab('cash-payments'),\n        children: [\"Cash Payments \", cashPendingOrders.length > 0 && /*#__PURE__*/_jsxDEV(\"span\", {\n          className: \"notification-badge\",\n          children: cashPendingOrders.length\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 920,\n          columnNumber: 58\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 916,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n        className: activeTab === 'tables' ? 'active' : '',\n        onClick: () => setActiveTab('tables'),\n        children: \"Tables\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 922,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n        className: activeTab === 'menu' ? 'active' : '',\n        onClick: () => setActiveTab('menu'),\n        children: \"Menu Management\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 928,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n        className: activeTab === 'users' ? 'active' : '',\n        onClick: () => setActiveTab('users'),\n        children: \"User Management\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 934,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 909,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"tab-content\",\n      children: [activeTab === 'orders' && renderOrders(), activeTab === 'cash-payments' && renderCashPayments(), activeTab === 'tables' && renderTables(), activeTab === 'menu' && renderMenuManagement(), activeTab === 'users' && renderUserManagement()]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 942,\n      columnNumber: 7\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 889,\n    columnNumber: 5\n  }, this);\n};\n_s(AdminDashboard, \"HCxINrAKmUGfJ5tY7RsrAqJ7tJc=\");\n_c = AdminDashboard;\nexport default AdminDashboard;\nvar _c;\n$RefreshReg$(_c, \"AdminDashboard\");", "map": {"version": 3, "names": ["React", "useState", "useEffect", "axios", "io", "jsxDEV", "_jsxDEV", "Fragment", "_Fragment", "API_BASE_URL", "process", "env", "REACT_APP_API_BASE_URL", "SOCKET_URL", "REACT_APP_SOCKET_URL", "AdminDashboard", "user", "onLogout", "_s", "activeTab", "setActiveTab", "orders", "setOrders", "tables", "setTables", "menuItems", "setMenuItems", "cashPendingOrders", "setCashPendingOrders", "users", "setUsers", "socket", "setSocket", "notifications", "setNotifications", "loading", "setLoading", "editingItem", "setEditingItem", "newItem", "setNewItem", "name", "description", "price", "category", "available", "showAddForm", "setShowAddForm", "editingUser", "setEditingUser", "newUser", "setNewUser", "username", "password", "email", "role", "showAddUserForm", "setShowAddUserForm", "getAuthHeaders", "token", "localStorage", "getItem", "headers", "fetchOrders", "fetchTables", "fetchMenuItems", "fetchCashPendingOrders", "fetchUsers", "newSocket", "on", "order", "prev", "updatedOrder", "map", "_id", "updatedTable", "table", "id", "Date", "now", "type", "message", "orderNumber", "filter", "o", "close", "response", "get", "data", "error", "_error$response", "console", "status", "log", "length", "_firstTable$qrCode", "firstTable", "qrCode", "_error$response2", "_error$response3", "_error$response4", "_error$response5", "updateOrderStatus", "orderId", "patch", "alert", "updateTableStatus", "tableId", "regenerateQRCodes", "window", "confirm", "post", "confirmCashPayment", "handleAddItem", "e", "preventDefault", "parseFloat", "handleUpdateItem", "itemId", "updatedData", "put", "handleDeleteItem", "delete", "handleAddUser", "handleUpdateUser", "userId", "handleDeleteUser", "renderOrders", "className", "children", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "style", "color", "fontStyle", "_order$table", "_order$totalAmount", "tableNumber", "createdAt", "toLocaleTimeString", "items", "item", "index", "_item$menuItem", "menuItem", "quantity", "toFixed", "totalAmount", "customerName", "marginTop", "padding", "background", "borderRadius", "specialInstructions", "trim", "border", "fontSize", "onClick", "disabled", "renderTables", "availableTables", "t", "occupiedTables", "reservedTables", "margin", "display", "gridTemplateColumns", "gap", "marginBottom", "textAlign", "fontWeight", "capacity", "src", "alt", "onError", "target", "nextS<PERSON>ling", "location", "origin", "renderMenuManagement", "groupedItems", "reduce", "acc", "push", "onSubmit", "value", "onChange", "required", "step", "rows", "Object", "keys", "char<PERSON>t", "toUpperCase", "slice", "EditItemForm", "onSave", "onCancel", "handleSubmit", "formData", "FormData", "defaultValue", "placeholder", "defaultChecked", "renderUserManagement", "userItem", "isActive", "toLocaleDateString", "renderCashPayments", "customerPhone", "notification", "_c", "$RefreshReg$"], "sources": ["C:/restraunat managment system/frontend/src/components/AdminDashboard.js"], "sourcesContent": ["import React, { useState, useEffect } from 'react';\nimport axios from 'axios';\nimport io from 'socket.io-client';\n\nconst API_BASE_URL = process.env.REACT_APP_API_BASE_URL;\nconst SOCKET_URL = process.env.REACT_APP_SOCKET_URL;\n\nconst AdminDashboard = ({ user, onLogout }) => {\n  const [activeTab, setActiveTab] = useState('orders');\n  const [orders, setOrders] = useState([]);\n  const [tables, setTables] = useState([]);\n  const [menuItems, setMenuItems] = useState([]);\n  const [cashPendingOrders, setCashPendingOrders] = useState([]);\n  const [users, setUsers] = useState([]);\n  const [socket, setSocket] = useState(null);\n  const [notifications, setNotifications] = useState([]);\n  const [loading, setLoading] = useState(true);\n\n  // Menu management states\n  const [editingItem, setEditingItem] = useState(null);\n  const [newItem, setNewItem] = useState({\n    name: '',\n    description: '',\n    price: '',\n    category: 'mains',\n    available: true\n  });\n  const [showAddForm, setShowAddForm] = useState(false);\n\n  // User management states\n  const [editingUser, setEditingUser] = useState(null);\n  const [newUser, setNewUser] = useState({\n    username: '',\n    password: '',\n    name: '',\n    email: '',\n    role: 'kitchen_staff'\n  });\n  const [showAddUserForm, setShowAddUserForm] = useState(false);\n\n  const getAuthHeaders = () => {\n    const token = localStorage.getItem('token');\n    return {\n      headers: {\n        'Authorization': `Bearer ${token}`\n      }\n    };\n  };\n\n  useEffect(() => {\n    fetchOrders();\n    fetchTables();\n    fetchMenuItems();\n    fetchCashPendingOrders();\n    fetchUsers();\n\n    const newSocket = io(SOCKET_URL);\n    setSocket(newSocket);\n\n    newSocket.on('newOrder', (order) => {\n      setOrders(prev => [order, ...prev]);\n    });\n\n    newSocket.on('orderStatusUpdate', (updatedOrder) => {\n      setOrders(prev => prev.map(order =>\n        order._id === updatedOrder._id ? updatedOrder : order\n      ));\n    });\n\n    newSocket.on('tableStatusUpdate', (updatedTable) => {\n      setTables(prev => prev.map(table =>\n        table._id === updatedTable._id ? updatedTable : table\n      ));\n    });\n\n    newSocket.on('cashPaymentPending', (order) => {\n      setCashPendingOrders(prev => [order, ...prev]);\n      setNotifications(prev => [...prev, {\n        id: Date.now(),\n        type: 'cash_payment',\n        message: `New cash payment order: ${order.orderNumber}`,\n        order\n      }]);\n    });\n\n    newSocket.on('cashPaymentConfirmed', (order) => {\n      setCashPendingOrders(prev => prev.filter(o => o._id !== order._id));\n    });\n\n    return () => newSocket.close();\n  }, []);\n\n  const fetchOrders = async () => {\n    try {\n      const response = await axios.get(`${API_BASE_URL}/api/admin/orders`, getAuthHeaders());\n      setOrders(response.data);\n      setLoading(false);\n    } catch (error) {\n      console.error('Error fetching orders:', error);\n      if (error.response?.status === 401) {\n        onLogout();\n      }\n      setLoading(false);\n    }\n  };\n\n  const fetchTables = async () => {\n    try {\n      const response = await axios.get(`${API_BASE_URL}/api/admin/tables`, getAuthHeaders());\n      console.log('Tables fetched:', response.data.length);\n      // Debug: Check if QR codes are present\n      if (response.data.length > 0) {\n        const firstTable = response.data[0];\n        console.log('First table QR code present:', !!firstTable.qrCode);\n        console.log('QR code length:', firstTable.qrCode?.length || 0);\n      }\n      setTables(response.data);\n    } catch (error) {\n      console.error('Error fetching tables:', error);\n      if (error.response?.status === 401) {\n        onLogout();\n      }\n    }\n  };\n\n  const fetchMenuItems = async () => {\n    try {\n      const response = await axios.get(`${API_BASE_URL}/api/admin/menu`, getAuthHeaders());\n      setMenuItems(response.data);\n    } catch (error) {\n      console.error('Error fetching menu items:', error);\n      if (error.response?.status === 401) {\n        onLogout();\n      }\n    }\n  };\n\n  const fetchCashPendingOrders = async () => {\n    try {\n      const response = await axios.get(`${API_BASE_URL}/api/admin/orders/cash-pending`, getAuthHeaders());\n      setCashPendingOrders(response.data);\n    } catch (error) {\n      console.error('Error fetching cash pending orders:', error);\n      if (error.response?.status === 401) {\n        onLogout();\n      }\n    }\n  };\n\n  const fetchUsers = async () => {\n    try {\n      const response = await axios.get(`${API_BASE_URL}/api/admin/users`, getAuthHeaders());\n      setUsers(response.data);\n    } catch (error) {\n      console.error('Error fetching users:', error);\n      if (error.response?.status === 401) {\n        onLogout();\n      }\n    }\n  };\n\n  const updateOrderStatus = async (orderId, status) => {\n    try {\n      await axios.patch(`${API_BASE_URL}/api/admin/orders/${orderId}/status`, { status }, getAuthHeaders());\n      fetchOrders();\n    } catch (error) {\n      console.error('Error updating order status:', error);\n      alert('Error updating order status. Please try again.');\n    }\n  };\n\n  const updateTableStatus = async (tableId, status) => {\n    try {\n      await axios.patch(`${API_BASE_URL}/api/admin/tables/${tableId}/status`, { status }, getAuthHeaders());\n      fetchTables();\n    } catch (error) {\n      console.error('Error updating table status:', error);\n      alert('Error updating table status. Please try again.');\n    }\n  };\n\n  const regenerateQRCodes = async () => {\n    if (window.confirm('Regenerate QR codes for all tables? This will update all existing QR codes.')) {\n      try {\n        await axios.post(`${API_BASE_URL}/api/admin/tables/regenerate-qr`, {}, getAuthHeaders());\n        alert('QR codes regenerated successfully!');\n        fetchTables();\n      } catch (error) {\n        console.error('Error regenerating QR codes:', error);\n        alert('Error regenerating QR codes. Please try again.');\n      }\n    }\n  };\n\n  const confirmCashPayment = async (orderId) => {\n    try {\n      await axios.post(`${API_BASE_URL}/api/orders/${orderId}/payment/cash/confirm`, {}, getAuthHeaders());\n      fetchCashPendingOrders();\n      fetchOrders();\n      fetchTables();\n    } catch (error) {\n      console.error('Error confirming cash payment:', error);\n      alert('Error confirming cash payment. Please try again.');\n    }\n  };\n\n  // Menu management functions\n  const handleAddItem = async (e) => {\n    e.preventDefault();\n    try {\n      await axios.post(\n        `${API_BASE_URL}/api/admin/menu`,\n        { ...newItem, price: parseFloat(newItem.price) },\n        getAuthHeaders()\n      );\n      setNewItem({ name: '', description: '', price: '', category: 'mains', available: true });\n      setShowAddForm(false);\n      fetchMenuItems();\n    } catch (error) {\n      console.error('Error adding menu item:', error);\n      alert('Error adding menu item. Please try again.');\n    }\n  };\n\n  const handleUpdateItem = async (itemId, updatedData) => {\n    try {\n      await axios.put(\n        `${API_BASE_URL}/api/admin/menu/${itemId}`,\n        updatedData,\n        getAuthHeaders()\n      );\n      setEditingItem(null);\n      fetchMenuItems();\n    } catch (error) {\n      console.error('Error updating menu item:', error);\n      alert('Error updating menu item. Please try again.');\n    }\n  };\n\n  const handleDeleteItem = async (itemId) => {\n    if (window.confirm('Are you sure you want to delete this menu item?')) {\n      try {\n        await axios.delete(\n          `${API_BASE_URL}/api/admin/menu/${itemId}`,\n          getAuthHeaders()\n        );\n        fetchMenuItems();\n      } catch (error) {\n        console.error('Error deleting menu item:', error);\n        alert('Error deleting menu item. Please try again.');\n      }\n    }\n  };\n\n  // User management functions\n  const handleAddUser = async (e) => {\n    e.preventDefault();\n    try {\n      await axios.post(`${API_BASE_URL}/api/admin/users`, newUser, getAuthHeaders());\n      setNewUser({ username: '', password: '', name: '', email: '', role: 'kitchen_staff' });\n      setShowAddUserForm(false);\n      fetchUsers();\n    } catch (error) {\n      console.error('Error adding user:', error);\n      alert('Error adding user. Please try again.');\n    }\n  };\n\n  const handleUpdateUser = async (userId, updatedData) => {\n    try {\n      await axios.put(`${API_BASE_URL}/api/admin/users/${userId}`, updatedData, getAuthHeaders());\n      setEditingUser(null);\n      fetchUsers();\n    } catch (error) {\n      console.error('Error updating user:', error);\n      alert('Error updating user. Please try again.');\n    }\n  };\n\n  const handleDeleteUser = async (userId) => {\n    if (window.confirm('Are you sure you want to delete this user?')) {\n      try {\n        await axios.delete(`${API_BASE_URL}/api/admin/users/${userId}`, getAuthHeaders());\n        fetchUsers();\n      } catch (error) {\n        console.error('Error deleting user:', error);\n        alert('Error deleting user. Please try again.');\n      }\n    }\n  };\n\n\n\n  const renderOrders = () => (\n    <div className=\"orders-section\">\n      <div className=\"section-header\">\n        <h2>📋 Orders ({orders.length})</h2>\n        {orders.length === 0 && (\n          <p style={{color: '#666', fontStyle: 'italic'}}>No orders yet today</p>\n        )}\n      </div>\n\n      {orders.map(order => (\n        <div key={order._id} className=\"order-card\">\n          <div className=\"order-header\">\n            <div className=\"order-info\">\n              <h3>🧾 Order #{order.orderNumber}</h3>\n              <div className=\"order-meta\">\n                📍 Table {order.table?.tableNumber} •\n                🕒 {new Date(order.createdAt).toLocaleTimeString()}\n              </div>\n            </div>\n            <span className={`status ${order.status}`}>{order.status}</span>\n          </div>\n\n          <div className=\"order-items\">\n            {order.items.map((item, index) => (\n              <div key={index} className=\"order-item\">\n                <div className=\"item-info\">\n                  <div className=\"item-name\">{item.menuItem?.name || 'Unknown Item'}</div>\n                  <div className=\"item-quantity\">Quantity: {item.quantity}</div>\n                </div>\n                <div className=\"item-price\">${(item.price * item.quantity).toFixed(2)}</div>\n              </div>\n            ))}\n          </div>\n\n          <div className=\"order-total\">\n            💰 Total: ${order.totalAmount?.toFixed(2) || '0.00'}\n          </div>\n\n          {order.customerName && (\n            <div style={{marginTop: '15px', padding: '10px', background: '#f8f9fa', borderRadius: '8px'}}>\n              <strong>👤 Customer:</strong> {order.customerName}\n            </div>\n          )}\n\n          {order.specialInstructions && order.specialInstructions.trim() !== '' ? (\n            <div style={{\n              marginTop: '10px',\n              padding: '12px',\n              background: '#fff3cd',\n              borderRadius: '8px',\n              border: '2px solid #ffc107',\n              fontSize: '0.95rem'\n            }}>\n              <strong>📝 Special Instructions:</strong> {order.specialInstructions}\n            </div>\n          ) : (\n            <div style={{\n              marginTop: '10px',\n              padding: '8px',\n              background: '#f8f9fa',\n              borderRadius: '6px',\n              color: '#6c757d',\n              fontSize: '0.9rem',\n              fontStyle: 'italic'\n            }}>\n              📝 No special instructions\n            </div>\n          )}\n\n          <div className=\"status-buttons\">\n            <button\n              className=\"btn-confirm\"\n              onClick={() => updateOrderStatus(order._id, 'confirmed')}\n              disabled={order.status !== 'pending'}\n            >\n              ✅ Confirm\n            </button>\n            <button\n              className=\"btn-prepare\"\n              onClick={() => updateOrderStatus(order._id, 'preparing')}\n              disabled={order.status !== 'confirmed'}\n            >\n              👨‍🍳 Preparing\n            </button>\n            <button\n              className=\"btn-ready\"\n              onClick={() => updateOrderStatus(order._id, 'ready')}\n              disabled={order.status !== 'preparing'}\n            >\n              🔔 Ready\n            </button>\n            <button\n              className=\"btn-serve\"\n              onClick={() => updateOrderStatus(order._id, 'served')}\n              disabled={order.status !== 'ready'}\n            >\n              🍽️ Served\n            </button>\n          </div>\n        </div>\n      ))}\n    </div>\n  );\n\n  const renderTables = () => {\n    const availableTables = tables.filter(t => t.status === 'available').length;\n    const occupiedTables = tables.filter(t => t.status === 'occupied').length;\n    const reservedTables = tables.filter(t => t.status === 'reserved').length;\n\n    return (\n      <div className=\"tables-section\">\n        <div className=\"section-header\">\n          <h2>🪑 Tables ({tables.length})</h2>\n          <div>\n            <p style={{color: '#666', margin: '0 0 10px 0'}}>Real-time table management with automatic status updates</p>\n            <button\n              className=\"btn-add-item\"\n              onClick={regenerateQRCodes}\n              style={{fontSize: '0.9rem', padding: '8px 16px'}}\n            >\n              🔄 Regenerate QR Codes\n            </button>\n          </div>\n        </div>\n\n        {/* Table Status Summary */}\n        <div style={{\n          display: 'grid',\n          gridTemplateColumns: 'repeat(auto-fit, minmax(200px, 1fr))',\n          gap: '20px',\n          marginBottom: '30px'\n        }}>\n          <div className=\"card\" style={{textAlign: 'center', background: '#d4edda'}}>\n            <h3 style={{color: '#155724', margin: '0 0 10px 0'}}>✅ Available</h3>\n            <div style={{fontSize: '2rem', fontWeight: 'bold', color: '#155724'}}>{availableTables}</div>\n          </div>\n          <div className=\"card\" style={{textAlign: 'center', background: '#f8d7da'}}>\n            <h3 style={{color: '#721c24', margin: '0 0 10px 0'}}>🔴 Occupied</h3>\n            <div style={{fontSize: '2rem', fontWeight: 'bold', color: '#721c24'}}>{occupiedTables}</div>\n          </div>\n          <div className=\"card\" style={{textAlign: 'center', background: '#fff3cd'}}>\n            <h3 style={{color: '#856404', margin: '0 0 10px 0'}}>🟡 Reserved</h3>\n            <div style={{fontSize: '2rem', fontWeight: 'bold', color: '#856404'}}>{reservedTables}</div>\n          </div>\n        </div>\n\n      <div className=\"tables-grid\">\n        {tables.map(table => (\n          <div key={table._id} className=\"table-card\">\n            <div className=\"table-number\">#{table.tableNumber}</div>\n            <div className=\"table-capacity\">\n              👥 Capacity: {table.capacity} people\n            </div>\n            <div className=\"table-status\">\n              <span className={`status ${table.status}`}>\n                {table.status === 'available' && '✅ Available'}\n                {table.status === 'occupied' && '🔴 Occupied'}\n                {table.status === 'reserved' && '🟡 Reserved'}\n              </span>\n            </div>\n\n            {table.status === 'occupied' && (\n              <div style={{\n                background: '#fff3cd',\n                padding: '8px',\n                borderRadius: '8px',\n                marginTop: '10px',\n                fontSize: '0.85rem',\n                color: '#856404'\n              }}>\n                🍽️ Currently serving customers\n              </div>\n            )}\n\n            <div className=\"table-actions\">\n              <button\n                className=\"btn-available\"\n                onClick={() => updateTableStatus(table._id, 'available')}\n              >\n                ✅ Available\n              </button>\n              <button\n                className=\"btn-occupied\"\n                onClick={() => updateTableStatus(table._id, 'occupied')}\n              >\n                🔴 Occupied\n              </button>\n              <button\n                className=\"btn-reserved\"\n                onClick={() => updateTableStatus(table._id, 'reserved')}\n              >\n                🟡 Reserved\n              </button>\n            </div>\n\n            <div className=\"qr-code\">\n              {table.qrCode ? (\n                <>\n                  <img\n                    src={table.qrCode}\n                    alt={`QR Code for Table ${table.tableNumber}`}\n                    onError={(e) => {\n                      console.error('QR Code image failed to load for table', table.tableNumber);\n                      e.target.style.display = 'none';\n                      e.target.nextSibling.style.display = 'block';\n                    }}\n                  />\n                  <div style={{display: 'none', color: '#e74c3c', fontSize: '0.9rem'}}>\n                    ❌ QR Code failed to load\n                  </div>\n                  <p>📱 Scan to order</p>\n                </>\n              ) : (\n                <div style={{color: '#e74c3c', fontSize: '0.9rem'}}>\n                  ❌ No QR Code available\n                  <br />\n                  <small>URL: {`${window.location.origin}/table/${table.tableNumber}`}</small>\n                </div>\n              )}\n            </div>\n          </div>\n        ))}\n      </div>\n    </div>\n    );\n  };\n\n  const renderMenuManagement = () => {\n    const groupedItems = menuItems.reduce((acc, item) => {\n      if (!acc[item.category]) acc[item.category] = [];\n      acc[item.category].push(item);\n      return acc;\n    }, {});\n\n    return (\n      <div className=\"menu-management-section\">\n        <div className=\"section-header\">\n          <h2>📋 Menu Management ({menuItems.length} items)</h2>\n          <button\n            className=\"btn-add-item\"\n            onClick={() => setShowAddForm(!showAddForm)}\n          >\n            {showAddForm ? '❌ Cancel' : '➕ Add New Item'}\n          </button>\n        </div>\n\n        {showAddForm && (\n          <div className=\"add-item-form\">\n            <h3>Add New Menu Item</h3>\n            <form onSubmit={handleAddItem}>\n              <div className=\"form-row\">\n                <div className=\"form-group\">\n                  <label>Name *</label>\n                  <input\n                    type=\"text\"\n                    value={newItem.name}\n                    onChange={(e) => setNewItem({...newItem, name: e.target.value})}\n                    required\n                  />\n                </div>\n                <div className=\"form-group\">\n                  <label>Price *</label>\n                  <input\n                    type=\"number\"\n                    step=\"0.01\"\n                    value={newItem.price}\n                    onChange={(e) => setNewItem({...newItem, price: e.target.value})}\n                    required\n                  />\n                </div>\n              </div>\n              <div className=\"form-group\">\n                <label>Category *</label>\n                <select\n                  value={newItem.category}\n                  onChange={(e) => setNewItem({...newItem, category: e.target.value})}\n                >\n                  <option value=\"appetizers\">Appetizers</option>\n                  <option value=\"mains\">Main Courses</option>\n                  <option value=\"desserts\">Desserts</option>\n                  <option value=\"beverages\">Beverages</option>\n                </select>\n              </div>\n              <div className=\"form-group\">\n                <label>Description</label>\n                <textarea\n                  value={newItem.description}\n                  onChange={(e) => setNewItem({...newItem, description: e.target.value})}\n                  rows=\"3\"\n                />\n              </div>\n              <div className=\"form-actions\">\n                <button type=\"submit\" className=\"btn-save\">Save Item</button>\n                <button type=\"button\" onClick={() => setShowAddForm(false)} className=\"btn-cancel\">Cancel</button>\n              </div>\n            </form>\n          </div>\n        )}\n\n        {Object.keys(groupedItems).map(category => (\n          <div key={category} className=\"menu-category-section\">\n            <h3 className=\"category-title\">\n              {category === 'appetizers' && '🥗'}\n              {category === 'mains' && '🍽️'}\n              {category === 'desserts' && '🍰'}\n              {category === 'beverages' && '🥤'}\n              {category.charAt(0).toUpperCase() + category.slice(1)} ({groupedItems[category].length})\n            </h3>\n\n            <div className=\"menu-items-grid\">\n              {groupedItems[category].map(item => (\n                <div key={item._id} className=\"menu-item-card\">\n                  {editingItem === item._id ? (\n                    <EditItemForm\n                      item={item}\n                      onSave={(data) => handleUpdateItem(item._id, data)}\n                      onCancel={() => setEditingItem(null)}\n                    />\n                  ) : (\n                    <>\n                      <div className=\"item-header\">\n                        <h4>{item.name}</h4>\n                        <span className=\"item-price\">${item.price.toFixed(2)}</span>\n                      </div>\n                      <p className=\"item-description\">{item.description}</p>\n                      <div className=\"item-status\">\n                        <span className={`availability ${item.available ? 'available' : 'unavailable'}`}>\n                          {item.available ? '✅ Available' : '❌ Unavailable'}\n                        </span>\n                      </div>\n                      <div className=\"item-actions\">\n                        <button\n                          className=\"btn-edit\"\n                          onClick={() => setEditingItem(item._id)}\n                        >\n                          ✏️ Edit\n                        </button>\n                        <button\n                          className=\"btn-delete\"\n                          onClick={() => handleDeleteItem(item._id)}\n                        >\n                          🗑️ Delete\n                        </button>\n                      </div>\n                    </>\n                  )}\n                </div>\n              ))}\n            </div>\n          </div>\n        ))}\n      </div>\n    );\n  };\n\n  // EditItemForm component\n  const EditItemForm = ({ item, onSave, onCancel }) => {\n    const handleSubmit = (e) => {\n      e.preventDefault();\n      const formData = new FormData(e.target);\n      const updatedData = {\n        name: formData.get('name'),\n        description: formData.get('description'),\n        price: parseFloat(formData.get('price')),\n        category: formData.get('category'),\n        available: formData.get('available') === 'on'\n      };\n      onSave(updatedData);\n    };\n\n    return (\n      <form onSubmit={handleSubmit} className=\"edit-item-form\">\n        <div className=\"form-group\">\n          <input\n            type=\"text\"\n            name=\"name\"\n            defaultValue={item.name}\n            required\n            placeholder=\"Item name\"\n          />\n        </div>\n        <div className=\"form-group\">\n          <input\n            type=\"number\"\n            name=\"price\"\n            step=\"0.01\"\n            defaultValue={item.price}\n            required\n            placeholder=\"Price\"\n          />\n        </div>\n        <div className=\"form-group\">\n          <select name=\"category\" defaultValue={item.category}>\n            <option value=\"appetizers\">Appetizers</option>\n            <option value=\"mains\">Main Courses</option>\n            <option value=\"desserts\">Desserts</option>\n            <option value=\"beverages\">Beverages</option>\n          </select>\n        </div>\n        <div className=\"form-group\">\n          <textarea\n            name=\"description\"\n            defaultValue={item.description}\n            rows=\"2\"\n            placeholder=\"Description\"\n          />\n        </div>\n        <div className=\"form-group\">\n          <label>\n            <input\n              type=\"checkbox\"\n              name=\"available\"\n              defaultChecked={item.available}\n            />\n            Available\n          </label>\n        </div>\n        <div className=\"form-actions\">\n          <button type=\"submit\" className=\"btn-save\">Save</button>\n          <button type=\"button\" onClick={onCancel} className=\"btn-cancel\">Cancel</button>\n        </div>\n      </form>\n    );\n  };\n\n  const renderUserManagement = () => (\n    <div className=\"user-management-section\">\n      <div className=\"section-header\">\n        <h2>👥 User Management ({users.length} users)</h2>\n        <button\n          className=\"btn-add-item\"\n          onClick={() => setShowAddUserForm(!showAddUserForm)}\n        >\n          {showAddUserForm ? '❌ Cancel' : '➕ Add New User'}\n        </button>\n      </div>\n\n      {showAddUserForm && (\n        <div className=\"add-item-form\">\n          <h3>Add New User</h3>\n          <form onSubmit={handleAddUser}>\n            <div className=\"form-row\">\n              <div className=\"form-group\">\n                <label>Username *</label>\n                <input\n                  type=\"text\"\n                  value={newUser.username}\n                  onChange={(e) => setNewUser({...newUser, username: e.target.value})}\n                  required\n                />\n              </div>\n              <div className=\"form-group\">\n                <label>Password *</label>\n                <input\n                  type=\"password\"\n                  value={newUser.password}\n                  onChange={(e) => setNewUser({...newUser, password: e.target.value})}\n                  required\n                />\n              </div>\n            </div>\n            <div className=\"form-row\">\n              <div className=\"form-group\">\n                <label>Full Name *</label>\n                <input\n                  type=\"text\"\n                  value={newUser.name}\n                  onChange={(e) => setNewUser({...newUser, name: e.target.value})}\n                  required\n                />\n              </div>\n              <div className=\"form-group\">\n                <label>Email *</label>\n                <input\n                  type=\"email\"\n                  value={newUser.email}\n                  onChange={(e) => setNewUser({...newUser, email: e.target.value})}\n                  required\n                />\n              </div>\n            </div>\n            <div className=\"form-group\">\n              <label>Role *</label>\n              <select\n                value={newUser.role}\n                onChange={(e) => setNewUser({...newUser, role: e.target.value})}\n              >\n                <option value=\"kitchen_staff\">Kitchen Staff</option>\n                <option value=\"manager\">Manager</option>\n                <option value=\"admin\">Admin</option>\n              </select>\n            </div>\n            <div className=\"form-actions\">\n              <button type=\"submit\" className=\"btn-save\">Create User</button>\n              <button type=\"button\" onClick={() => setShowAddUserForm(false)} className=\"btn-cancel\">Cancel</button>\n            </div>\n          </form>\n        </div>\n      )}\n\n      <div className=\"users-grid\">\n        {users.map(userItem => (\n          <div key={userItem._id} className=\"user-card\">\n            <div className=\"user-header\">\n              <h4>{userItem.name}</h4>\n              <span className={`user-role ${userItem.role}`}>\n                {userItem.role === 'admin' && '👨‍💻 Admin'}\n                {userItem.role === 'manager' && '👨‍💼 Manager'}\n                {userItem.role === 'kitchen_staff' && '👨‍🍳 Kitchen Staff'}\n              </span>\n            </div>\n            <div className=\"user-details\">\n              <p><strong>Username:</strong> {userItem.username}</p>\n              <p><strong>Email:</strong> {userItem.email}</p>\n              <p><strong>Status:</strong> {userItem.isActive ? '✅ Active' : '❌ Inactive'}</p>\n              <p><strong>Created:</strong> {new Date(userItem.createdAt).toLocaleDateString()}</p>\n            </div>\n            <div className=\"user-actions\">\n              <button\n                className=\"btn-edit\"\n                onClick={() => setEditingUser(userItem._id)}\n              >\n                ✏️ Edit\n              </button>\n              <button\n                className=\"btn-delete\"\n                onClick={() => handleDeleteUser(userItem._id)}\n                disabled={userItem._id === user?._id}\n              >\n                🗑️ Delete\n              </button>\n            </div>\n          </div>\n        ))}\n      </div>\n    </div>\n  );\n\n  const renderCashPayments = () => (\n    <div className=\"cash-payments-section\">\n      <div className=\"section-header\">\n        <h2>💰 Cash Payment Confirmations ({cashPendingOrders.length})</h2>\n        {cashPendingOrders.length === 0 && (\n          <p style={{color: '#666', fontStyle: 'italic'}}>No pending cash payments</p>\n        )}\n      </div>\n\n      {cashPendingOrders.map(order => (\n        <div key={order._id} className=\"cash-payment-card\">\n          <div className=\"order-header\">\n            <div className=\"order-info\">\n              <h3>Order #{order.orderNumber}</h3>\n              <div className=\"order-meta\">\n                <span className=\"table-info\">📍 Table {order.table.tableNumber}</span>\n                <span className=\"order-time\">🕒 {new Date(order.createdAt).toLocaleTimeString()}</span>\n                <span className=\"payment-method\">💰 Cash Payment</span>\n              </div>\n            </div>\n            <div className=\"order-total\">\n              <strong>${order.totalAmount.toFixed(2)}</strong>\n            </div>\n          </div>\n\n          <div className=\"customer-details\">\n            <p><strong>Customer:</strong> {order.customerName}</p>\n            <p><strong>Phone:</strong> {order.customerPhone}</p>\n            {order.specialInstructions && (\n              <p><strong>Special Instructions:</strong> {order.specialInstructions}</p>\n            )}\n          </div>\n\n          <div className=\"order-items\">\n            <h4>Items:</h4>\n            {order.items.map((item, index) => (\n              <div key={index} className=\"order-item\">\n                <span>{item.quantity}x {item.menuItem.name}</span>\n                <span>${(item.price * item.quantity).toFixed(2)}</span>\n              </div>\n            ))}\n          </div>\n\n          <div className=\"cash-payment-actions\">\n            <button\n              className=\"btn-confirm-cash\"\n              onClick={() => confirmCashPayment(order._id)}\n            >\n              ✅ Confirm Cash Payment\n            </button>\n          </div>\n        </div>\n      ))}\n    </div>\n  );\n\n  return (\n    <div className=\"admin-dashboard\">\n      <div className=\"admin-header\">\n        <div className=\"header-left\">\n          <h1>👨‍💼 Admin Dashboard</h1>\n          <p>Welcome, {user?.name || 'Administrator'}</p>\n        </div>\n        <div className=\"header-right\">\n          <div className=\"notifications\">\n            {notifications.map(notification => (\n              <div key={notification.id} className=\"notification\">\n                {notification.message}\n              </div>\n            ))}\n          </div>\n          <button className=\"logout-btn\" onClick={onLogout}>\n            🚪 Logout\n          </button>\n        </div>\n      </div>\n\n      <div className=\"admin-tabs\">\n        <button\n          className={activeTab === 'orders' ? 'active' : ''}\n          onClick={() => setActiveTab('orders')}\n        >\n          Orders\n        </button>\n        <button\n          className={activeTab === 'cash-payments' ? 'active' : ''}\n          onClick={() => setActiveTab('cash-payments')}\n        >\n          Cash Payments {cashPendingOrders.length > 0 && <span className=\"notification-badge\">{cashPendingOrders.length}</span>}\n        </button>\n        <button\n          className={activeTab === 'tables' ? 'active' : ''}\n          onClick={() => setActiveTab('tables')}\n        >\n          Tables\n        </button>\n        <button\n          className={activeTab === 'menu' ? 'active' : ''}\n          onClick={() => setActiveTab('menu')}\n        >\n          Menu Management\n        </button>\n        <button\n          className={activeTab === 'users' ? 'active' : ''}\n          onClick={() => setActiveTab('users')}\n        >\n          User Management\n        </button>\n      </div>\n\n      <div className=\"tab-content\">\n        {activeTab === 'orders' && renderOrders()}\n        {activeTab === 'cash-payments' && renderCashPayments()}\n        {activeTab === 'tables' && renderTables()}\n        {activeTab === 'menu' && renderMenuManagement()}\n        {activeTab === 'users' && renderUserManagement()}\n      </div>\n    </div>\n  );\n};\n\nexport default AdminDashboard;\n"], "mappings": ";;AAAA,OAAOA,KAAK,IAAIC,QAAQ,EAAEC,SAAS,QAAQ,OAAO;AAClD,OAAOC,KAAK,MAAM,OAAO;AACzB,OAAOC,EAAE,MAAM,kBAAkB;AAAC,SAAAC,MAAA,IAAAC,OAAA,EAAAC,QAAA,IAAAC,SAAA;AAElC,MAAMC,YAAY,GAAGC,OAAO,CAACC,GAAG,CAACC,sBAAsB;AACvD,MAAMC,UAAU,GAAGH,OAAO,CAACC,GAAG,CAACG,oBAAoB;AAEnD,MAAMC,cAAc,GAAGA,CAAC;EAAEC,IAAI;EAAEC;AAAS,CAAC,KAAK;EAAAC,EAAA;EAC7C,MAAM,CAACC,SAAS,EAAEC,YAAY,CAAC,GAAGnB,QAAQ,CAAC,QAAQ,CAAC;EACpD,MAAM,CAACoB,MAAM,EAAEC,SAAS,CAAC,GAAGrB,QAAQ,CAAC,EAAE,CAAC;EACxC,MAAM,CAACsB,MAAM,EAAEC,SAAS,CAAC,GAAGvB,QAAQ,CAAC,EAAE,CAAC;EACxC,MAAM,CAACwB,SAAS,EAAEC,YAAY,CAAC,GAAGzB,QAAQ,CAAC,EAAE,CAAC;EAC9C,MAAM,CAAC0B,iBAAiB,EAAEC,oBAAoB,CAAC,GAAG3B,QAAQ,CAAC,EAAE,CAAC;EAC9D,MAAM,CAAC4B,KAAK,EAAEC,QAAQ,CAAC,GAAG7B,QAAQ,CAAC,EAAE,CAAC;EACtC,MAAM,CAAC8B,MAAM,EAAEC,SAAS,CAAC,GAAG/B,QAAQ,CAAC,IAAI,CAAC;EAC1C,MAAM,CAACgC,aAAa,EAAEC,gBAAgB,CAAC,GAAGjC,QAAQ,CAAC,EAAE,CAAC;EACtD,MAAM,CAACkC,OAAO,EAAEC,UAAU,CAAC,GAAGnC,QAAQ,CAAC,IAAI,CAAC;;EAE5C;EACA,MAAM,CAACoC,WAAW,EAAEC,cAAc,CAAC,GAAGrC,QAAQ,CAAC,IAAI,CAAC;EACpD,MAAM,CAACsC,OAAO,EAAEC,UAAU,CAAC,GAAGvC,QAAQ,CAAC;IACrCwC,IAAI,EAAE,EAAE;IACRC,WAAW,EAAE,EAAE;IACfC,KAAK,EAAE,EAAE;IACTC,QAAQ,EAAE,OAAO;IACjBC,SAAS,EAAE;EACb,CAAC,CAAC;EACF,MAAM,CAACC,WAAW,EAAEC,cAAc,CAAC,GAAG9C,QAAQ,CAAC,KAAK,CAAC;;EAErD;EACA,MAAM,CAAC+C,WAAW,EAAEC,cAAc,CAAC,GAAGhD,QAAQ,CAAC,IAAI,CAAC;EACpD,MAAM,CAACiD,OAAO,EAAEC,UAAU,CAAC,GAAGlD,QAAQ,CAAC;IACrCmD,QAAQ,EAAE,EAAE;IACZC,QAAQ,EAAE,EAAE;IACZZ,IAAI,EAAE,EAAE;IACRa,KAAK,EAAE,EAAE;IACTC,IAAI,EAAE;EACR,CAAC,CAAC;EACF,MAAM,CAACC,eAAe,EAAEC,kBAAkB,CAAC,GAAGxD,QAAQ,CAAC,KAAK,CAAC;EAE7D,MAAMyD,cAAc,GAAGA,CAAA,KAAM;IAC3B,MAAMC,KAAK,GAAGC,YAAY,CAACC,OAAO,CAAC,OAAO,CAAC;IAC3C,OAAO;MACLC,OAAO,EAAE;QACP,eAAe,EAAE,UAAUH,KAAK;MAClC;IACF,CAAC;EACH,CAAC;EAEDzD,SAAS,CAAC,MAAM;IACd6D,WAAW,CAAC,CAAC;IACbC,WAAW,CAAC,CAAC;IACbC,cAAc,CAAC,CAAC;IAChBC,sBAAsB,CAAC,CAAC;IACxBC,UAAU,CAAC,CAAC;IAEZ,MAAMC,SAAS,GAAGhE,EAAE,CAACS,UAAU,CAAC;IAChCmB,SAAS,CAACoC,SAAS,CAAC;IAEpBA,SAAS,CAACC,EAAE,CAAC,UAAU,EAAGC,KAAK,IAAK;MAClChD,SAAS,CAACiD,IAAI,IAAI,CAACD,KAAK,EAAE,GAAGC,IAAI,CAAC,CAAC;IACrC,CAAC,CAAC;IAEFH,SAAS,CAACC,EAAE,CAAC,mBAAmB,EAAGG,YAAY,IAAK;MAClDlD,SAAS,CAACiD,IAAI,IAAIA,IAAI,CAACE,GAAG,CAACH,KAAK,IAC9BA,KAAK,CAACI,GAAG,KAAKF,YAAY,CAACE,GAAG,GAAGF,YAAY,GAAGF,KAClD,CAAC,CAAC;IACJ,CAAC,CAAC;IAEFF,SAAS,CAACC,EAAE,CAAC,mBAAmB,EAAGM,YAAY,IAAK;MAClDnD,SAAS,CAAC+C,IAAI,IAAIA,IAAI,CAACE,GAAG,CAACG,KAAK,IAC9BA,KAAK,CAACF,GAAG,KAAKC,YAAY,CAACD,GAAG,GAAGC,YAAY,GAAGC,KAClD,CAAC,CAAC;IACJ,CAAC,CAAC;IAEFR,SAAS,CAACC,EAAE,CAAC,oBAAoB,EAAGC,KAAK,IAAK;MAC5C1C,oBAAoB,CAAC2C,IAAI,IAAI,CAACD,KAAK,EAAE,GAAGC,IAAI,CAAC,CAAC;MAC9CrC,gBAAgB,CAACqC,IAAI,IAAI,CAAC,GAAGA,IAAI,EAAE;QACjCM,EAAE,EAAEC,IAAI,CAACC,GAAG,CAAC,CAAC;QACdC,IAAI,EAAE,cAAc;QACpBC,OAAO,EAAE,2BAA2BX,KAAK,CAACY,WAAW,EAAE;QACvDZ;MACF,CAAC,CAAC,CAAC;IACL,CAAC,CAAC;IAEFF,SAAS,CAACC,EAAE,CAAC,sBAAsB,EAAGC,KAAK,IAAK;MAC9C1C,oBAAoB,CAAC2C,IAAI,IAAIA,IAAI,CAACY,MAAM,CAACC,CAAC,IAAIA,CAAC,CAACV,GAAG,KAAKJ,KAAK,CAACI,GAAG,CAAC,CAAC;IACrE,CAAC,CAAC;IAEF,OAAO,MAAMN,SAAS,CAACiB,KAAK,CAAC,CAAC;EAChC,CAAC,EAAE,EAAE,CAAC;EAEN,MAAMtB,WAAW,GAAG,MAAAA,CAAA,KAAY;IAC9B,IAAI;MACF,MAAMuB,QAAQ,GAAG,MAAMnF,KAAK,CAACoF,GAAG,CAAC,GAAG9E,YAAY,mBAAmB,EAAEiD,cAAc,CAAC,CAAC,CAAC;MACtFpC,SAAS,CAACgE,QAAQ,CAACE,IAAI,CAAC;MACxBpD,UAAU,CAAC,KAAK,CAAC;IACnB,CAAC,CAAC,OAAOqD,KAAK,EAAE;MAAA,IAAAC,eAAA;MACdC,OAAO,CAACF,KAAK,CAAC,wBAAwB,EAAEA,KAAK,CAAC;MAC9C,IAAI,EAAAC,eAAA,GAAAD,KAAK,CAACH,QAAQ,cAAAI,eAAA,uBAAdA,eAAA,CAAgBE,MAAM,MAAK,GAAG,EAAE;QAClC3E,QAAQ,CAAC,CAAC;MACZ;MACAmB,UAAU,CAAC,KAAK,CAAC;IACnB;EACF,CAAC;EAED,MAAM4B,WAAW,GAAG,MAAAA,CAAA,KAAY;IAC9B,IAAI;MACF,MAAMsB,QAAQ,GAAG,MAAMnF,KAAK,CAACoF,GAAG,CAAC,GAAG9E,YAAY,mBAAmB,EAAEiD,cAAc,CAAC,CAAC,CAAC;MACtFiC,OAAO,CAACE,GAAG,CAAC,iBAAiB,EAAEP,QAAQ,CAACE,IAAI,CAACM,MAAM,CAAC;MACpD;MACA,IAAIR,QAAQ,CAACE,IAAI,CAACM,MAAM,GAAG,CAAC,EAAE;QAAA,IAAAC,kBAAA;QAC5B,MAAMC,UAAU,GAAGV,QAAQ,CAACE,IAAI,CAAC,CAAC,CAAC;QACnCG,OAAO,CAACE,GAAG,CAAC,8BAA8B,EAAE,CAAC,CAACG,UAAU,CAACC,MAAM,CAAC;QAChEN,OAAO,CAACE,GAAG,CAAC,iBAAiB,EAAE,EAAAE,kBAAA,GAAAC,UAAU,CAACC,MAAM,cAAAF,kBAAA,uBAAjBA,kBAAA,CAAmBD,MAAM,KAAI,CAAC,CAAC;MAChE;MACAtE,SAAS,CAAC8D,QAAQ,CAACE,IAAI,CAAC;IAC1B,CAAC,CAAC,OAAOC,KAAK,EAAE;MAAA,IAAAS,gBAAA;MACdP,OAAO,CAACF,KAAK,CAAC,wBAAwB,EAAEA,KAAK,CAAC;MAC9C,IAAI,EAAAS,gBAAA,GAAAT,KAAK,CAACH,QAAQ,cAAAY,gBAAA,uBAAdA,gBAAA,CAAgBN,MAAM,MAAK,GAAG,EAAE;QAClC3E,QAAQ,CAAC,CAAC;MACZ;IACF;EACF,CAAC;EAED,MAAMgD,cAAc,GAAG,MAAAA,CAAA,KAAY;IACjC,IAAI;MACF,MAAMqB,QAAQ,GAAG,MAAMnF,KAAK,CAACoF,GAAG,CAAC,GAAG9E,YAAY,iBAAiB,EAAEiD,cAAc,CAAC,CAAC,CAAC;MACpFhC,YAAY,CAAC4D,QAAQ,CAACE,IAAI,CAAC;IAC7B,CAAC,CAAC,OAAOC,KAAK,EAAE;MAAA,IAAAU,gBAAA;MACdR,OAAO,CAACF,KAAK,CAAC,4BAA4B,EAAEA,KAAK,CAAC;MAClD,IAAI,EAAAU,gBAAA,GAAAV,KAAK,CAACH,QAAQ,cAAAa,gBAAA,uBAAdA,gBAAA,CAAgBP,MAAM,MAAK,GAAG,EAAE;QAClC3E,QAAQ,CAAC,CAAC;MACZ;IACF;EACF,CAAC;EAED,MAAMiD,sBAAsB,GAAG,MAAAA,CAAA,KAAY;IACzC,IAAI;MACF,MAAMoB,QAAQ,GAAG,MAAMnF,KAAK,CAACoF,GAAG,CAAC,GAAG9E,YAAY,gCAAgC,EAAEiD,cAAc,CAAC,CAAC,CAAC;MACnG9B,oBAAoB,CAAC0D,QAAQ,CAACE,IAAI,CAAC;IACrC,CAAC,CAAC,OAAOC,KAAK,EAAE;MAAA,IAAAW,gBAAA;MACdT,OAAO,CAACF,KAAK,CAAC,qCAAqC,EAAEA,KAAK,CAAC;MAC3D,IAAI,EAAAW,gBAAA,GAAAX,KAAK,CAACH,QAAQ,cAAAc,gBAAA,uBAAdA,gBAAA,CAAgBR,MAAM,MAAK,GAAG,EAAE;QAClC3E,QAAQ,CAAC,CAAC;MACZ;IACF;EACF,CAAC;EAED,MAAMkD,UAAU,GAAG,MAAAA,CAAA,KAAY;IAC7B,IAAI;MACF,MAAMmB,QAAQ,GAAG,MAAMnF,KAAK,CAACoF,GAAG,CAAC,GAAG9E,YAAY,kBAAkB,EAAEiD,cAAc,CAAC,CAAC,CAAC;MACrF5B,QAAQ,CAACwD,QAAQ,CAACE,IAAI,CAAC;IACzB,CAAC,CAAC,OAAOC,KAAK,EAAE;MAAA,IAAAY,gBAAA;MACdV,OAAO,CAACF,KAAK,CAAC,uBAAuB,EAAEA,KAAK,CAAC;MAC7C,IAAI,EAAAY,gBAAA,GAAAZ,KAAK,CAACH,QAAQ,cAAAe,gBAAA,uBAAdA,gBAAA,CAAgBT,MAAM,MAAK,GAAG,EAAE;QAClC3E,QAAQ,CAAC,CAAC;MACZ;IACF;EACF,CAAC;EAED,MAAMqF,iBAAiB,GAAG,MAAAA,CAAOC,OAAO,EAAEX,MAAM,KAAK;IACnD,IAAI;MACF,MAAMzF,KAAK,CAACqG,KAAK,CAAC,GAAG/F,YAAY,qBAAqB8F,OAAO,SAAS,EAAE;QAAEX;MAAO,CAAC,EAAElC,cAAc,CAAC,CAAC,CAAC;MACrGK,WAAW,CAAC,CAAC;IACf,CAAC,CAAC,OAAO0B,KAAK,EAAE;MACdE,OAAO,CAACF,KAAK,CAAC,8BAA8B,EAAEA,KAAK,CAAC;MACpDgB,KAAK,CAAC,gDAAgD,CAAC;IACzD;EACF,CAAC;EAED,MAAMC,iBAAiB,GAAG,MAAAA,CAAOC,OAAO,EAAEf,MAAM,KAAK;IACnD,IAAI;MACF,MAAMzF,KAAK,CAACqG,KAAK,CAAC,GAAG/F,YAAY,qBAAqBkG,OAAO,SAAS,EAAE;QAAEf;MAAO,CAAC,EAAElC,cAAc,CAAC,CAAC,CAAC;MACrGM,WAAW,CAAC,CAAC;IACf,CAAC,CAAC,OAAOyB,KAAK,EAAE;MACdE,OAAO,CAACF,KAAK,CAAC,8BAA8B,EAAEA,KAAK,CAAC;MACpDgB,KAAK,CAAC,gDAAgD,CAAC;IACzD;EACF,CAAC;EAED,MAAMG,iBAAiB,GAAG,MAAAA,CAAA,KAAY;IACpC,IAAIC,MAAM,CAACC,OAAO,CAAC,6EAA6E,CAAC,EAAE;MACjG,IAAI;QACF,MAAM3G,KAAK,CAAC4G,IAAI,CAAC,GAAGtG,YAAY,iCAAiC,EAAE,CAAC,CAAC,EAAEiD,cAAc,CAAC,CAAC,CAAC;QACxF+C,KAAK,CAAC,oCAAoC,CAAC;QAC3CzC,WAAW,CAAC,CAAC;MACf,CAAC,CAAC,OAAOyB,KAAK,EAAE;QACdE,OAAO,CAACF,KAAK,CAAC,8BAA8B,EAAEA,KAAK,CAAC;QACpDgB,KAAK,CAAC,gDAAgD,CAAC;MACzD;IACF;EACF,CAAC;EAED,MAAMO,kBAAkB,GAAG,MAAOT,OAAO,IAAK;IAC5C,IAAI;MACF,MAAMpG,KAAK,CAAC4G,IAAI,CAAC,GAAGtG,YAAY,eAAe8F,OAAO,uBAAuB,EAAE,CAAC,CAAC,EAAE7C,cAAc,CAAC,CAAC,CAAC;MACpGQ,sBAAsB,CAAC,CAAC;MACxBH,WAAW,CAAC,CAAC;MACbC,WAAW,CAAC,CAAC;IACf,CAAC,CAAC,OAAOyB,KAAK,EAAE;MACdE,OAAO,CAACF,KAAK,CAAC,gCAAgC,EAAEA,KAAK,CAAC;MACtDgB,KAAK,CAAC,kDAAkD,CAAC;IAC3D;EACF,CAAC;;EAED;EACA,MAAMQ,aAAa,GAAG,MAAOC,CAAC,IAAK;IACjCA,CAAC,CAACC,cAAc,CAAC,CAAC;IAClB,IAAI;MACF,MAAMhH,KAAK,CAAC4G,IAAI,CACd,GAAGtG,YAAY,iBAAiB,EAChC;QAAE,GAAG8B,OAAO;QAAEI,KAAK,EAAEyE,UAAU,CAAC7E,OAAO,CAACI,KAAK;MAAE,CAAC,EAChDe,cAAc,CAAC,CACjB,CAAC;MACDlB,UAAU,CAAC;QAAEC,IAAI,EAAE,EAAE;QAAEC,WAAW,EAAE,EAAE;QAAEC,KAAK,EAAE,EAAE;QAAEC,QAAQ,EAAE,OAAO;QAAEC,SAAS,EAAE;MAAK,CAAC,CAAC;MACxFE,cAAc,CAAC,KAAK,CAAC;MACrBkB,cAAc,CAAC,CAAC;IAClB,CAAC,CAAC,OAAOwB,KAAK,EAAE;MACdE,OAAO,CAACF,KAAK,CAAC,yBAAyB,EAAEA,KAAK,CAAC;MAC/CgB,KAAK,CAAC,2CAA2C,CAAC;IACpD;EACF,CAAC;EAED,MAAMY,gBAAgB,GAAG,MAAAA,CAAOC,MAAM,EAAEC,WAAW,KAAK;IACtD,IAAI;MACF,MAAMpH,KAAK,CAACqH,GAAG,CACb,GAAG/G,YAAY,mBAAmB6G,MAAM,EAAE,EAC1CC,WAAW,EACX7D,cAAc,CAAC,CACjB,CAAC;MACDpB,cAAc,CAAC,IAAI,CAAC;MACpB2B,cAAc,CAAC,CAAC;IAClB,CAAC,CAAC,OAAOwB,KAAK,EAAE;MACdE,OAAO,CAACF,KAAK,CAAC,2BAA2B,EAAEA,KAAK,CAAC;MACjDgB,KAAK,CAAC,6CAA6C,CAAC;IACtD;EACF,CAAC;EAED,MAAMgB,gBAAgB,GAAG,MAAOH,MAAM,IAAK;IACzC,IAAIT,MAAM,CAACC,OAAO,CAAC,iDAAiD,CAAC,EAAE;MACrE,IAAI;QACF,MAAM3G,KAAK,CAACuH,MAAM,CAChB,GAAGjH,YAAY,mBAAmB6G,MAAM,EAAE,EAC1C5D,cAAc,CAAC,CACjB,CAAC;QACDO,cAAc,CAAC,CAAC;MAClB,CAAC,CAAC,OAAOwB,KAAK,EAAE;QACdE,OAAO,CAACF,KAAK,CAAC,2BAA2B,EAAEA,KAAK,CAAC;QACjDgB,KAAK,CAAC,6CAA6C,CAAC;MACtD;IACF;EACF,CAAC;;EAED;EACA,MAAMkB,aAAa,GAAG,MAAOT,CAAC,IAAK;IACjCA,CAAC,CAACC,cAAc,CAAC,CAAC;IAClB,IAAI;MACF,MAAMhH,KAAK,CAAC4G,IAAI,CAAC,GAAGtG,YAAY,kBAAkB,EAAEyC,OAAO,EAAEQ,cAAc,CAAC,CAAC,CAAC;MAC9EP,UAAU,CAAC;QAAEC,QAAQ,EAAE,EAAE;QAAEC,QAAQ,EAAE,EAAE;QAAEZ,IAAI,EAAE,EAAE;QAAEa,KAAK,EAAE,EAAE;QAAEC,IAAI,EAAE;MAAgB,CAAC,CAAC;MACtFE,kBAAkB,CAAC,KAAK,CAAC;MACzBU,UAAU,CAAC,CAAC;IACd,CAAC,CAAC,OAAOsB,KAAK,EAAE;MACdE,OAAO,CAACF,KAAK,CAAC,oBAAoB,EAAEA,KAAK,CAAC;MAC1CgB,KAAK,CAAC,sCAAsC,CAAC;IAC/C;EACF,CAAC;EAED,MAAMmB,gBAAgB,GAAG,MAAAA,CAAOC,MAAM,EAAEN,WAAW,KAAK;IACtD,IAAI;MACF,MAAMpH,KAAK,CAACqH,GAAG,CAAC,GAAG/G,YAAY,oBAAoBoH,MAAM,EAAE,EAAEN,WAAW,EAAE7D,cAAc,CAAC,CAAC,CAAC;MAC3FT,cAAc,CAAC,IAAI,CAAC;MACpBkB,UAAU,CAAC,CAAC;IACd,CAAC,CAAC,OAAOsB,KAAK,EAAE;MACdE,OAAO,CAACF,KAAK,CAAC,sBAAsB,EAAEA,KAAK,CAAC;MAC5CgB,KAAK,CAAC,wCAAwC,CAAC;IACjD;EACF,CAAC;EAED,MAAMqB,gBAAgB,GAAG,MAAOD,MAAM,IAAK;IACzC,IAAIhB,MAAM,CAACC,OAAO,CAAC,4CAA4C,CAAC,EAAE;MAChE,IAAI;QACF,MAAM3G,KAAK,CAACuH,MAAM,CAAC,GAAGjH,YAAY,oBAAoBoH,MAAM,EAAE,EAAEnE,cAAc,CAAC,CAAC,CAAC;QACjFS,UAAU,CAAC,CAAC;MACd,CAAC,CAAC,OAAOsB,KAAK,EAAE;QACdE,OAAO,CAACF,KAAK,CAAC,sBAAsB,EAAEA,KAAK,CAAC;QAC5CgB,KAAK,CAAC,wCAAwC,CAAC;MACjD;IACF;EACF,CAAC;EAID,MAAMsB,YAAY,GAAGA,CAAA,kBACnBzH,OAAA;IAAK0H,SAAS,EAAC,gBAAgB;IAAAC,QAAA,gBAC7B3H,OAAA;MAAK0H,SAAS,EAAC,gBAAgB;MAAAC,QAAA,gBAC7B3H,OAAA;QAAA2H,QAAA,GAAI,uBAAW,EAAC5G,MAAM,CAACyE,MAAM,EAAC,GAAC;MAAA;QAAAoC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAI,CAAC,EACnChH,MAAM,CAACyE,MAAM,KAAK,CAAC,iBAClBxF,OAAA;QAAGgI,KAAK,EAAE;UAACC,KAAK,EAAE,MAAM;UAAEC,SAAS,EAAE;QAAQ,CAAE;QAAAP,QAAA,EAAC;MAAmB;QAAAC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAG,CACvE;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACE,CAAC,EAELhH,MAAM,CAACoD,GAAG,CAACH,KAAK;MAAA,IAAAmE,YAAA,EAAAC,kBAAA;MAAA,oBACfpI,OAAA;QAAqB0H,SAAS,EAAC,YAAY;QAAAC,QAAA,gBACzC3H,OAAA;UAAK0H,SAAS,EAAC,cAAc;UAAAC,QAAA,gBAC3B3H,OAAA;YAAK0H,SAAS,EAAC,YAAY;YAAAC,QAAA,gBACzB3H,OAAA;cAAA2H,QAAA,GAAI,sBAAU,EAAC3D,KAAK,CAACY,WAAW;YAAA;cAAAgD,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAK,CAAC,eACtC/H,OAAA;cAAK0H,SAAS,EAAC,YAAY;cAAAC,QAAA,GAAC,qBACjB,GAAAQ,YAAA,GAACnE,KAAK,CAACM,KAAK,cAAA6D,YAAA,uBAAXA,YAAA,CAAaE,WAAW,EAAC,uBAChC,EAAC,IAAI7D,IAAI,CAACR,KAAK,CAACsE,SAAS,CAAC,CAACC,kBAAkB,CAAC,CAAC;YAAA;cAAAX,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAC/C,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACH,CAAC,eACN/H,OAAA;YAAM0H,SAAS,EAAE,UAAU1D,KAAK,CAACsB,MAAM,EAAG;YAAAqC,QAAA,EAAE3D,KAAK,CAACsB;UAAM;YAAAsC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAO,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAC7D,CAAC,eAEN/H,OAAA;UAAK0H,SAAS,EAAC,aAAa;UAAAC,QAAA,EACzB3D,KAAK,CAACwE,KAAK,CAACrE,GAAG,CAAC,CAACsE,IAAI,EAAEC,KAAK;YAAA,IAAAC,cAAA;YAAA,oBAC3B3I,OAAA;cAAiB0H,SAAS,EAAC,YAAY;cAAAC,QAAA,gBACrC3H,OAAA;gBAAK0H,SAAS,EAAC,WAAW;gBAAAC,QAAA,gBACxB3H,OAAA;kBAAK0H,SAAS,EAAC,WAAW;kBAAAC,QAAA,EAAE,EAAAgB,cAAA,GAAAF,IAAI,CAACG,QAAQ,cAAAD,cAAA,uBAAbA,cAAA,CAAexG,IAAI,KAAI;gBAAc;kBAAAyF,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAM,CAAC,eACxE/H,OAAA;kBAAK0H,SAAS,EAAC,eAAe;kBAAAC,QAAA,GAAC,YAAU,EAACc,IAAI,CAACI,QAAQ;gBAAA;kBAAAjB,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAM,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAC3D,CAAC,eACN/H,OAAA;gBAAK0H,SAAS,EAAC,YAAY;gBAAAC,QAAA,GAAC,GAAC,EAAC,CAACc,IAAI,CAACpG,KAAK,GAAGoG,IAAI,CAACI,QAAQ,EAAEC,OAAO,CAAC,CAAC,CAAC;cAAA;gBAAAlB,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAM,CAAC;YAAA,GALpEW,KAAK;cAAAd,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAMV,CAAC;UAAA,CACP;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACC,CAAC,eAEN/H,OAAA;UAAK0H,SAAS,EAAC,aAAa;UAAAC,QAAA,GAAC,uBAChB,EAAC,EAAAS,kBAAA,GAAApE,KAAK,CAAC+E,WAAW,cAAAX,kBAAA,uBAAjBA,kBAAA,CAAmBU,OAAO,CAAC,CAAC,CAAC,KAAI,MAAM;QAAA;UAAAlB,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAChD,CAAC,EAEL/D,KAAK,CAACgF,YAAY,iBACjBhJ,OAAA;UAAKgI,KAAK,EAAE;YAACiB,SAAS,EAAE,MAAM;YAAEC,OAAO,EAAE,MAAM;YAAEC,UAAU,EAAE,SAAS;YAAEC,YAAY,EAAE;UAAK,CAAE;UAAAzB,QAAA,gBAC3F3H,OAAA;YAAA2H,QAAA,EAAQ;UAAY;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAQ,CAAC,KAAC,EAAC/D,KAAK,CAACgF,YAAY;QAAA;UAAApB,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAC9C,CACN,EAEA/D,KAAK,CAACqF,mBAAmB,IAAIrF,KAAK,CAACqF,mBAAmB,CAACC,IAAI,CAAC,CAAC,KAAK,EAAE,gBACnEtJ,OAAA;UAAKgI,KAAK,EAAE;YACViB,SAAS,EAAE,MAAM;YACjBC,OAAO,EAAE,MAAM;YACfC,UAAU,EAAE,SAAS;YACrBC,YAAY,EAAE,KAAK;YACnBG,MAAM,EAAE,mBAAmB;YAC3BC,QAAQ,EAAE;UACZ,CAAE;UAAA7B,QAAA,gBACA3H,OAAA;YAAA2H,QAAA,EAAQ;UAAwB;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAQ,CAAC,KAAC,EAAC/D,KAAK,CAACqF,mBAAmB;QAAA;UAAAzB,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACjE,CAAC,gBAEN/H,OAAA;UAAKgI,KAAK,EAAE;YACViB,SAAS,EAAE,MAAM;YACjBC,OAAO,EAAE,KAAK;YACdC,UAAU,EAAE,SAAS;YACrBC,YAAY,EAAE,KAAK;YACnBnB,KAAK,EAAE,SAAS;YAChBuB,QAAQ,EAAE,QAAQ;YAClBtB,SAAS,EAAE;UACb,CAAE;UAAAP,QAAA,EAAC;QAEH;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAK,CACN,eAED/H,OAAA;UAAK0H,SAAS,EAAC,gBAAgB;UAAAC,QAAA,gBAC7B3H,OAAA;YACE0H,SAAS,EAAC,aAAa;YACvB+B,OAAO,EAAEA,CAAA,KAAMzD,iBAAiB,CAAChC,KAAK,CAACI,GAAG,EAAE,WAAW,CAAE;YACzDsF,QAAQ,EAAE1F,KAAK,CAACsB,MAAM,KAAK,SAAU;YAAAqC,QAAA,EACtC;UAED;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAQ,CAAC,eACT/H,OAAA;YACE0H,SAAS,EAAC,aAAa;YACvB+B,OAAO,EAAEA,CAAA,KAAMzD,iBAAiB,CAAChC,KAAK,CAACI,GAAG,EAAE,WAAW,CAAE;YACzDsF,QAAQ,EAAE1F,KAAK,CAACsB,MAAM,KAAK,WAAY;YAAAqC,QAAA,EACxC;UAED;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAQ,CAAC,eACT/H,OAAA;YACE0H,SAAS,EAAC,WAAW;YACrB+B,OAAO,EAAEA,CAAA,KAAMzD,iBAAiB,CAAChC,KAAK,CAACI,GAAG,EAAE,OAAO,CAAE;YACrDsF,QAAQ,EAAE1F,KAAK,CAACsB,MAAM,KAAK,WAAY;YAAAqC,QAAA,EACxC;UAED;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAQ,CAAC,eACT/H,OAAA;YACE0H,SAAS,EAAC,WAAW;YACrB+B,OAAO,EAAEA,CAAA,KAAMzD,iBAAiB,CAAChC,KAAK,CAACI,GAAG,EAAE,QAAQ,CAAE;YACtDsF,QAAQ,EAAE1F,KAAK,CAACsB,MAAM,KAAK,OAAQ;YAAAqC,QAAA,EACpC;UAED;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAQ,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACN,CAAC;MAAA,GAxFE/D,KAAK,CAACI,GAAG;QAAAwD,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAyFd,CAAC;IAAA,CACP,CAAC;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACC,CACN;EAED,MAAM4B,YAAY,GAAGA,CAAA,KAAM;IACzB,MAAMC,eAAe,GAAG3I,MAAM,CAAC4D,MAAM,CAACgF,CAAC,IAAIA,CAAC,CAACvE,MAAM,KAAK,WAAW,CAAC,CAACE,MAAM;IAC3E,MAAMsE,cAAc,GAAG7I,MAAM,CAAC4D,MAAM,CAACgF,CAAC,IAAIA,CAAC,CAACvE,MAAM,KAAK,UAAU,CAAC,CAACE,MAAM;IACzE,MAAMuE,cAAc,GAAG9I,MAAM,CAAC4D,MAAM,CAACgF,CAAC,IAAIA,CAAC,CAACvE,MAAM,KAAK,UAAU,CAAC,CAACE,MAAM;IAEzE,oBACExF,OAAA;MAAK0H,SAAS,EAAC,gBAAgB;MAAAC,QAAA,gBAC7B3H,OAAA;QAAK0H,SAAS,EAAC,gBAAgB;QAAAC,QAAA,gBAC7B3H,OAAA;UAAA2H,QAAA,GAAI,uBAAW,EAAC1G,MAAM,CAACuE,MAAM,EAAC,GAAC;QAAA;UAAAoC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAI,CAAC,eACpC/H,OAAA;UAAA2H,QAAA,gBACE3H,OAAA;YAAGgI,KAAK,EAAE;cAACC,KAAK,EAAE,MAAM;cAAE+B,MAAM,EAAE;YAAY,CAAE;YAAArC,QAAA,EAAC;UAAwD;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAG,CAAC,eAC7G/H,OAAA;YACE0H,SAAS,EAAC,cAAc;YACxB+B,OAAO,EAAEnD,iBAAkB;YAC3B0B,KAAK,EAAE;cAACwB,QAAQ,EAAE,QAAQ;cAAEN,OAAO,EAAE;YAAU,CAAE;YAAAvB,QAAA,EAClD;UAED;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAQ,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACN,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH,CAAC,eAGN/H,OAAA;QAAKgI,KAAK,EAAE;UACViC,OAAO,EAAE,MAAM;UACfC,mBAAmB,EAAE,sCAAsC;UAC3DC,GAAG,EAAE,MAAM;UACXC,YAAY,EAAE;QAChB,CAAE;QAAAzC,QAAA,gBACA3H,OAAA;UAAK0H,SAAS,EAAC,MAAM;UAACM,KAAK,EAAE;YAACqC,SAAS,EAAE,QAAQ;YAAElB,UAAU,EAAE;UAAS,CAAE;UAAAxB,QAAA,gBACxE3H,OAAA;YAAIgI,KAAK,EAAE;cAACC,KAAK,EAAE,SAAS;cAAE+B,MAAM,EAAE;YAAY,CAAE;YAAArC,QAAA,EAAC;UAAW;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAI,CAAC,eACrE/H,OAAA;YAAKgI,KAAK,EAAE;cAACwB,QAAQ,EAAE,MAAM;cAAEc,UAAU,EAAE,MAAM;cAAErC,KAAK,EAAE;YAAS,CAAE;YAAAN,QAAA,EAAEiC;UAAe;YAAAhC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAM,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAC1F,CAAC,eACN/H,OAAA;UAAK0H,SAAS,EAAC,MAAM;UAACM,KAAK,EAAE;YAACqC,SAAS,EAAE,QAAQ;YAAElB,UAAU,EAAE;UAAS,CAAE;UAAAxB,QAAA,gBACxE3H,OAAA;YAAIgI,KAAK,EAAE;cAACC,KAAK,EAAE,SAAS;cAAE+B,MAAM,EAAE;YAAY,CAAE;YAAArC,QAAA,EAAC;UAAW;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAI,CAAC,eACrE/H,OAAA;YAAKgI,KAAK,EAAE;cAACwB,QAAQ,EAAE,MAAM;cAAEc,UAAU,EAAE,MAAM;cAAErC,KAAK,EAAE;YAAS,CAAE;YAAAN,QAAA,EAAEmC;UAAc;YAAAlC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAM,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACzF,CAAC,eACN/H,OAAA;UAAK0H,SAAS,EAAC,MAAM;UAACM,KAAK,EAAE;YAACqC,SAAS,EAAE,QAAQ;YAAElB,UAAU,EAAE;UAAS,CAAE;UAAAxB,QAAA,gBACxE3H,OAAA;YAAIgI,KAAK,EAAE;cAACC,KAAK,EAAE,SAAS;cAAE+B,MAAM,EAAE;YAAY,CAAE;YAAArC,QAAA,EAAC;UAAW;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAI,CAAC,eACrE/H,OAAA;YAAKgI,KAAK,EAAE;cAACwB,QAAQ,EAAE,MAAM;cAAEc,UAAU,EAAE,MAAM;cAAErC,KAAK,EAAE;YAAS,CAAE;YAAAN,QAAA,EAAEoC;UAAc;YAAAnC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAM,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACzF,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH,CAAC,eAER/H,OAAA;QAAK0H,SAAS,EAAC,aAAa;QAAAC,QAAA,EACzB1G,MAAM,CAACkD,GAAG,CAACG,KAAK,iBACftE,OAAA;UAAqB0H,SAAS,EAAC,YAAY;UAAAC,QAAA,gBACzC3H,OAAA;YAAK0H,SAAS,EAAC,cAAc;YAAAC,QAAA,GAAC,GAAC,EAACrD,KAAK,CAAC+D,WAAW;UAAA;YAAAT,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAM,CAAC,eACxD/H,OAAA;YAAK0H,SAAS,EAAC,gBAAgB;YAAAC,QAAA,GAAC,yBACjB,EAACrD,KAAK,CAACiG,QAAQ,EAAC,SAC/B;UAAA;YAAA3C,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAK,CAAC,eACN/H,OAAA;YAAK0H,SAAS,EAAC,cAAc;YAAAC,QAAA,eAC3B3H,OAAA;cAAM0H,SAAS,EAAE,UAAUpD,KAAK,CAACgB,MAAM,EAAG;cAAAqC,QAAA,GACvCrD,KAAK,CAACgB,MAAM,KAAK,WAAW,IAAI,aAAa,EAC7ChB,KAAK,CAACgB,MAAM,KAAK,UAAU,IAAI,aAAa,EAC5ChB,KAAK,CAACgB,MAAM,KAAK,UAAU,IAAI,aAAa;YAAA;cAAAsC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACzC;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACJ,CAAC,EAELzD,KAAK,CAACgB,MAAM,KAAK,UAAU,iBAC1BtF,OAAA;YAAKgI,KAAK,EAAE;cACVmB,UAAU,EAAE,SAAS;cACrBD,OAAO,EAAE,KAAK;cACdE,YAAY,EAAE,KAAK;cACnBH,SAAS,EAAE,MAAM;cACjBO,QAAQ,EAAE,SAAS;cACnBvB,KAAK,EAAE;YACT,CAAE;YAAAN,QAAA,EAAC;UAEH;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAK,CACN,eAED/H,OAAA;YAAK0H,SAAS,EAAC,eAAe;YAAAC,QAAA,gBAC5B3H,OAAA;cACE0H,SAAS,EAAC,eAAe;cACzB+B,OAAO,EAAEA,CAAA,KAAMrD,iBAAiB,CAAC9B,KAAK,CAACF,GAAG,EAAE,WAAW,CAAE;cAAAuD,QAAA,EAC1D;YAED;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAQ,CAAC,eACT/H,OAAA;cACE0H,SAAS,EAAC,cAAc;cACxB+B,OAAO,EAAEA,CAAA,KAAMrD,iBAAiB,CAAC9B,KAAK,CAACF,GAAG,EAAE,UAAU,CAAE;cAAAuD,QAAA,EACzD;YAED;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAQ,CAAC,eACT/H,OAAA;cACE0H,SAAS,EAAC,cAAc;cACxB+B,OAAO,EAAEA,CAAA,KAAMrD,iBAAiB,CAAC9B,KAAK,CAACF,GAAG,EAAE,UAAU,CAAE;cAAAuD,QAAA,EACzD;YAED;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAQ,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACN,CAAC,eAEN/H,OAAA;YAAK0H,SAAS,EAAC,SAAS;YAAAC,QAAA,EACrBrD,KAAK,CAACqB,MAAM,gBACX3F,OAAA,CAAAE,SAAA;cAAAyH,QAAA,gBACE3H,OAAA;gBACEwK,GAAG,EAAElG,KAAK,CAACqB,MAAO;gBAClB8E,GAAG,EAAE,qBAAqBnG,KAAK,CAAC+D,WAAW,EAAG;gBAC9CqC,OAAO,EAAG9D,CAAC,IAAK;kBACdvB,OAAO,CAACF,KAAK,CAAC,wCAAwC,EAAEb,KAAK,CAAC+D,WAAW,CAAC;kBAC1EzB,CAAC,CAAC+D,MAAM,CAAC3C,KAAK,CAACiC,OAAO,GAAG,MAAM;kBAC/BrD,CAAC,CAAC+D,MAAM,CAACC,WAAW,CAAC5C,KAAK,CAACiC,OAAO,GAAG,OAAO;gBAC9C;cAAE;gBAAArC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACH,CAAC,eACF/H,OAAA;gBAAKgI,KAAK,EAAE;kBAACiC,OAAO,EAAE,MAAM;kBAAEhC,KAAK,EAAE,SAAS;kBAAEuB,QAAQ,EAAE;gBAAQ,CAAE;gBAAA7B,QAAA,EAAC;cAErE;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAK,CAAC,eACN/H,OAAA;gBAAA2H,QAAA,EAAG;cAAgB;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAG,CAAC;YAAA,eACvB,CAAC,gBAEH/H,OAAA;cAAKgI,KAAK,EAAE;gBAACC,KAAK,EAAE,SAAS;gBAAEuB,QAAQ,EAAE;cAAQ,CAAE;cAAA7B,QAAA,GAAC,6BAElD,eAAA3H,OAAA;gBAAA4H,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAK,CAAC,eACN/H,OAAA;gBAAA2H,QAAA,GAAO,OAAK,EAAC,GAAGpB,MAAM,CAACsE,QAAQ,CAACC,MAAM,UAAUxG,KAAK,CAAC+D,WAAW,EAAE;cAAA;gBAAAT,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAQ,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACzE;UACN;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACE,CAAC;QAAA,GAvEEzD,KAAK,CAACF,GAAG;UAAAwD,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAwEd,CACN;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACC,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH,CAAC;EAER,CAAC;EAED,MAAMgD,oBAAoB,GAAGA,CAAA,KAAM;IACjC,MAAMC,YAAY,GAAG7J,SAAS,CAAC8J,MAAM,CAAC,CAACC,GAAG,EAAEzC,IAAI,KAAK;MACnD,IAAI,CAACyC,GAAG,CAACzC,IAAI,CAACnG,QAAQ,CAAC,EAAE4I,GAAG,CAACzC,IAAI,CAACnG,QAAQ,CAAC,GAAG,EAAE;MAChD4I,GAAG,CAACzC,IAAI,CAACnG,QAAQ,CAAC,CAAC6I,IAAI,CAAC1C,IAAI,CAAC;MAC7B,OAAOyC,GAAG;IACZ,CAAC,EAAE,CAAC,CAAC,CAAC;IAEN,oBACElL,OAAA;MAAK0H,SAAS,EAAC,yBAAyB;MAAAC,QAAA,gBACtC3H,OAAA;QAAK0H,SAAS,EAAC,gBAAgB;QAAAC,QAAA,gBAC7B3H,OAAA;UAAA2H,QAAA,GAAI,gCAAoB,EAACxG,SAAS,CAACqE,MAAM,EAAC,SAAO;QAAA;UAAAoC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAI,CAAC,eACtD/H,OAAA;UACE0H,SAAS,EAAC,cAAc;UACxB+B,OAAO,EAAEA,CAAA,KAAMhH,cAAc,CAAC,CAACD,WAAW,CAAE;UAAAmF,QAAA,EAE3CnF,WAAW,GAAG,UAAU,GAAG;QAAgB;UAAAoF,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACtC,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACN,CAAC,EAELvF,WAAW,iBACVxC,OAAA;QAAK0H,SAAS,EAAC,eAAe;QAAAC,QAAA,gBAC5B3H,OAAA;UAAA2H,QAAA,EAAI;QAAiB;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAI,CAAC,eAC1B/H,OAAA;UAAMoL,QAAQ,EAAEzE,aAAc;UAAAgB,QAAA,gBAC5B3H,OAAA;YAAK0H,SAAS,EAAC,UAAU;YAAAC,QAAA,gBACvB3H,OAAA;cAAK0H,SAAS,EAAC,YAAY;cAAAC,QAAA,gBACzB3H,OAAA;gBAAA2H,QAAA,EAAO;cAAM;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAO,CAAC,eACrB/H,OAAA;gBACE0E,IAAI,EAAC,MAAM;gBACX2G,KAAK,EAAEpJ,OAAO,CAACE,IAAK;gBACpBmJ,QAAQ,EAAG1E,CAAC,IAAK1E,UAAU,CAAC;kBAAC,GAAGD,OAAO;kBAAEE,IAAI,EAAEyE,CAAC,CAAC+D,MAAM,CAACU;gBAAK,CAAC,CAAE;gBAChEE,QAAQ;cAAA;gBAAA3D,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACT,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACC,CAAC,eACN/H,OAAA;cAAK0H,SAAS,EAAC,YAAY;cAAAC,QAAA,gBACzB3H,OAAA;gBAAA2H,QAAA,EAAO;cAAO;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAO,CAAC,eACtB/H,OAAA;gBACE0E,IAAI,EAAC,QAAQ;gBACb8G,IAAI,EAAC,MAAM;gBACXH,KAAK,EAAEpJ,OAAO,CAACI,KAAM;gBACrBiJ,QAAQ,EAAG1E,CAAC,IAAK1E,UAAU,CAAC;kBAAC,GAAGD,OAAO;kBAAEI,KAAK,EAAEuE,CAAC,CAAC+D,MAAM,CAACU;gBAAK,CAAC,CAAE;gBACjEE,QAAQ;cAAA;gBAAA3D,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACT,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACC,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACH,CAAC,eACN/H,OAAA;YAAK0H,SAAS,EAAC,YAAY;YAAAC,QAAA,gBACzB3H,OAAA;cAAA2H,QAAA,EAAO;YAAU;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAO,CAAC,eACzB/H,OAAA;cACEqL,KAAK,EAAEpJ,OAAO,CAACK,QAAS;cACxBgJ,QAAQ,EAAG1E,CAAC,IAAK1E,UAAU,CAAC;gBAAC,GAAGD,OAAO;gBAAEK,QAAQ,EAAEsE,CAAC,CAAC+D,MAAM,CAACU;cAAK,CAAC,CAAE;cAAA1D,QAAA,gBAEpE3H,OAAA;gBAAQqL,KAAK,EAAC,YAAY;gBAAA1D,QAAA,EAAC;cAAU;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAQ,CAAC,eAC9C/H,OAAA;gBAAQqL,KAAK,EAAC,OAAO;gBAAA1D,QAAA,EAAC;cAAY;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAQ,CAAC,eAC3C/H,OAAA;gBAAQqL,KAAK,EAAC,UAAU;gBAAA1D,QAAA,EAAC;cAAQ;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAQ,CAAC,eAC1C/H,OAAA;gBAAQqL,KAAK,EAAC,WAAW;gBAAA1D,QAAA,EAAC;cAAS;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAQ,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACtC,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACN,CAAC,eACN/H,OAAA;YAAK0H,SAAS,EAAC,YAAY;YAAAC,QAAA,gBACzB3H,OAAA;cAAA2H,QAAA,EAAO;YAAW;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAO,CAAC,eAC1B/H,OAAA;cACEqL,KAAK,EAAEpJ,OAAO,CAACG,WAAY;cAC3BkJ,QAAQ,EAAG1E,CAAC,IAAK1E,UAAU,CAAC;gBAAC,GAAGD,OAAO;gBAAEG,WAAW,EAAEwE,CAAC,CAAC+D,MAAM,CAACU;cAAK,CAAC,CAAE;cACvEI,IAAI,EAAC;YAAG;cAAA7D,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACT,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACC,CAAC,eACN/H,OAAA;YAAK0H,SAAS,EAAC,cAAc;YAAAC,QAAA,gBAC3B3H,OAAA;cAAQ0E,IAAI,EAAC,QAAQ;cAACgD,SAAS,EAAC,UAAU;cAAAC,QAAA,EAAC;YAAS;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAQ,CAAC,eAC7D/H,OAAA;cAAQ0E,IAAI,EAAC,QAAQ;cAAC+E,OAAO,EAAEA,CAAA,KAAMhH,cAAc,CAAC,KAAK,CAAE;cAACiF,SAAS,EAAC,YAAY;cAAAC,QAAA,EAAC;YAAM;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAQ,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAC/F,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACF,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACJ,CACN,EAEA2D,MAAM,CAACC,IAAI,CAACX,YAAY,CAAC,CAAC7G,GAAG,CAAC7B,QAAQ,iBACrCtC,OAAA;QAAoB0H,SAAS,EAAC,uBAAuB;QAAAC,QAAA,gBACnD3H,OAAA;UAAI0H,SAAS,EAAC,gBAAgB;UAAAC,QAAA,GAC3BrF,QAAQ,KAAK,YAAY,IAAI,IAAI,EACjCA,QAAQ,KAAK,OAAO,IAAI,KAAK,EAC7BA,QAAQ,KAAK,UAAU,IAAI,IAAI,EAC/BA,QAAQ,KAAK,WAAW,IAAI,IAAI,EAChCA,QAAQ,CAACsJ,MAAM,CAAC,CAAC,CAAC,CAACC,WAAW,CAAC,CAAC,GAAGvJ,QAAQ,CAACwJ,KAAK,CAAC,CAAC,CAAC,EAAC,IAAE,EAACd,YAAY,CAAC1I,QAAQ,CAAC,CAACkD,MAAM,EAAC,GACzF;QAAA;UAAAoC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAI,CAAC,eAEL/H,OAAA;UAAK0H,SAAS,EAAC,iBAAiB;UAAAC,QAAA,EAC7BqD,YAAY,CAAC1I,QAAQ,CAAC,CAAC6B,GAAG,CAACsE,IAAI,iBAC9BzI,OAAA;YAAoB0H,SAAS,EAAC,gBAAgB;YAAAC,QAAA,EAC3C5F,WAAW,KAAK0G,IAAI,CAACrE,GAAG,gBACvBpE,OAAA,CAAC+L,YAAY;cACXtD,IAAI,EAAEA,IAAK;cACXuD,MAAM,EAAG9G,IAAI,IAAK6B,gBAAgB,CAAC0B,IAAI,CAACrE,GAAG,EAAEc,IAAI,CAAE;cACnD+G,QAAQ,EAAEA,CAAA,KAAMjK,cAAc,CAAC,IAAI;YAAE;cAAA4F,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACtC,CAAC,gBAEF/H,OAAA,CAAAE,SAAA;cAAAyH,QAAA,gBACE3H,OAAA;gBAAK0H,SAAS,EAAC,aAAa;gBAAAC,QAAA,gBAC1B3H,OAAA;kBAAA2H,QAAA,EAAKc,IAAI,CAACtG;gBAAI;kBAAAyF,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAK,CAAC,eACpB/H,OAAA;kBAAM0H,SAAS,EAAC,YAAY;kBAAAC,QAAA,GAAC,GAAC,EAACc,IAAI,CAACpG,KAAK,CAACyG,OAAO,CAAC,CAAC,CAAC;gBAAA;kBAAAlB,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAO,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACzD,CAAC,eACN/H,OAAA;gBAAG0H,SAAS,EAAC,kBAAkB;gBAAAC,QAAA,EAAEc,IAAI,CAACrG;cAAW;gBAAAwF,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAI,CAAC,eACtD/H,OAAA;gBAAK0H,SAAS,EAAC,aAAa;gBAAAC,QAAA,eAC1B3H,OAAA;kBAAM0H,SAAS,EAAE,gBAAgBe,IAAI,CAAClG,SAAS,GAAG,WAAW,GAAG,aAAa,EAAG;kBAAAoF,QAAA,EAC7Ec,IAAI,CAAClG,SAAS,GAAG,aAAa,GAAG;gBAAe;kBAAAqF,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAC7C;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACJ,CAAC,eACN/H,OAAA;gBAAK0H,SAAS,EAAC,cAAc;gBAAAC,QAAA,gBAC3B3H,OAAA;kBACE0H,SAAS,EAAC,UAAU;kBACpB+B,OAAO,EAAEA,CAAA,KAAMzH,cAAc,CAACyG,IAAI,CAACrE,GAAG,CAAE;kBAAAuD,QAAA,EACzC;gBAED;kBAAAC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAQ,CAAC,eACT/H,OAAA;kBACE0H,SAAS,EAAC,YAAY;kBACtB+B,OAAO,EAAEA,CAAA,KAAMtC,gBAAgB,CAACsB,IAAI,CAACrE,GAAG,CAAE;kBAAAuD,QAAA,EAC3C;gBAED;kBAAAC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAQ,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACN,CAAC;YAAA,eACN;UACH,GAlCOU,IAAI,CAACrE,GAAG;YAAAwD,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAmCb,CACN;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACC,CAAC;MAAA,GAhDEzF,QAAQ;QAAAsF,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAiDb,CACN,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACC,CAAC;EAEV,CAAC;;EAED;EACA,MAAMgE,YAAY,GAAGA,CAAC;IAAEtD,IAAI;IAAEuD,MAAM;IAAEC;EAAS,CAAC,KAAK;IACnD,MAAMC,YAAY,GAAItF,CAAC,IAAK;MAC1BA,CAAC,CAACC,cAAc,CAAC,CAAC;MAClB,MAAMsF,QAAQ,GAAG,IAAIC,QAAQ,CAACxF,CAAC,CAAC+D,MAAM,CAAC;MACvC,MAAM1D,WAAW,GAAG;QAClB9E,IAAI,EAAEgK,QAAQ,CAAClH,GAAG,CAAC,MAAM,CAAC;QAC1B7C,WAAW,EAAE+J,QAAQ,CAAClH,GAAG,CAAC,aAAa,CAAC;QACxC5C,KAAK,EAAEyE,UAAU,CAACqF,QAAQ,CAAClH,GAAG,CAAC,OAAO,CAAC,CAAC;QACxC3C,QAAQ,EAAE6J,QAAQ,CAAClH,GAAG,CAAC,UAAU,CAAC;QAClC1C,SAAS,EAAE4J,QAAQ,CAAClH,GAAG,CAAC,WAAW,CAAC,KAAK;MAC3C,CAAC;MACD+G,MAAM,CAAC/E,WAAW,CAAC;IACrB,CAAC;IAED,oBACEjH,OAAA;MAAMoL,QAAQ,EAAEc,YAAa;MAACxE,SAAS,EAAC,gBAAgB;MAAAC,QAAA,gBACtD3H,OAAA;QAAK0H,SAAS,EAAC,YAAY;QAAAC,QAAA,eACzB3H,OAAA;UACE0E,IAAI,EAAC,MAAM;UACXvC,IAAI,EAAC,MAAM;UACXkK,YAAY,EAAE5D,IAAI,CAACtG,IAAK;UACxBoJ,QAAQ;UACRe,WAAW,EAAC;QAAW;UAAA1E,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACxB;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACC,CAAC,eACN/H,OAAA;QAAK0H,SAAS,EAAC,YAAY;QAAAC,QAAA,eACzB3H,OAAA;UACE0E,IAAI,EAAC,QAAQ;UACbvC,IAAI,EAAC,OAAO;UACZqJ,IAAI,EAAC,MAAM;UACXa,YAAY,EAAE5D,IAAI,CAACpG,KAAM;UACzBkJ,QAAQ;UACRe,WAAW,EAAC;QAAO;UAAA1E,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACpB;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACC,CAAC,eACN/H,OAAA;QAAK0H,SAAS,EAAC,YAAY;QAAAC,QAAA,eACzB3H,OAAA;UAAQmC,IAAI,EAAC,UAAU;UAACkK,YAAY,EAAE5D,IAAI,CAACnG,QAAS;UAAAqF,QAAA,gBAClD3H,OAAA;YAAQqL,KAAK,EAAC,YAAY;YAAA1D,QAAA,EAAC;UAAU;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAQ,CAAC,eAC9C/H,OAAA;YAAQqL,KAAK,EAAC,OAAO;YAAA1D,QAAA,EAAC;UAAY;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAQ,CAAC,eAC3C/H,OAAA;YAAQqL,KAAK,EAAC,UAAU;YAAA1D,QAAA,EAAC;UAAQ;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAQ,CAAC,eAC1C/H,OAAA;YAAQqL,KAAK,EAAC,WAAW;YAAA1D,QAAA,EAAC;UAAS;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAQ,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACtC;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACN,CAAC,eACN/H,OAAA;QAAK0H,SAAS,EAAC,YAAY;QAAAC,QAAA,eACzB3H,OAAA;UACEmC,IAAI,EAAC,aAAa;UAClBkK,YAAY,EAAE5D,IAAI,CAACrG,WAAY;UAC/BqJ,IAAI,EAAC,GAAG;UACRa,WAAW,EAAC;QAAa;UAAA1E,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAC1B;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACC,CAAC,eACN/H,OAAA;QAAK0H,SAAS,EAAC,YAAY;QAAAC,QAAA,eACzB3H,OAAA;UAAA2H,QAAA,gBACE3H,OAAA;YACE0E,IAAI,EAAC,UAAU;YACfvC,IAAI,EAAC,WAAW;YAChBoK,cAAc,EAAE9D,IAAI,CAAClG;UAAU;YAAAqF,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAChC,CAAC,aAEJ;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAO;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACL,CAAC,eACN/H,OAAA;QAAK0H,SAAS,EAAC,cAAc;QAAAC,QAAA,gBAC3B3H,OAAA;UAAQ0E,IAAI,EAAC,QAAQ;UAACgD,SAAS,EAAC,UAAU;UAAAC,QAAA,EAAC;QAAI;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAQ,CAAC,eACxD/H,OAAA;UAAQ0E,IAAI,EAAC,QAAQ;UAAC+E,OAAO,EAAEwC,QAAS;UAACvE,SAAS,EAAC,YAAY;UAAAC,QAAA,EAAC;QAAM;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAQ,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAC5E,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACF,CAAC;EAEX,CAAC;EAED,MAAMyE,oBAAoB,GAAGA,CAAA,kBAC3BxM,OAAA;IAAK0H,SAAS,EAAC,yBAAyB;IAAAC,QAAA,gBACtC3H,OAAA;MAAK0H,SAAS,EAAC,gBAAgB;MAAAC,QAAA,gBAC7B3H,OAAA;QAAA2H,QAAA,GAAI,gCAAoB,EAACpG,KAAK,CAACiE,MAAM,EAAC,SAAO;MAAA;QAAAoC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAI,CAAC,eAClD/H,OAAA;QACE0H,SAAS,EAAC,cAAc;QACxB+B,OAAO,EAAEA,CAAA,KAAMtG,kBAAkB,CAAC,CAACD,eAAe,CAAE;QAAAyE,QAAA,EAEnDzE,eAAe,GAAG,UAAU,GAAG;MAAgB;QAAA0E,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAC1C,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACN,CAAC,EAEL7E,eAAe,iBACdlD,OAAA;MAAK0H,SAAS,EAAC,eAAe;MAAAC,QAAA,gBAC5B3H,OAAA;QAAA2H,QAAA,EAAI;MAAY;QAAAC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAI,CAAC,eACrB/H,OAAA;QAAMoL,QAAQ,EAAE/D,aAAc;QAAAM,QAAA,gBAC5B3H,OAAA;UAAK0H,SAAS,EAAC,UAAU;UAAAC,QAAA,gBACvB3H,OAAA;YAAK0H,SAAS,EAAC,YAAY;YAAAC,QAAA,gBACzB3H,OAAA;cAAA2H,QAAA,EAAO;YAAU;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAO,CAAC,eACzB/H,OAAA;cACE0E,IAAI,EAAC,MAAM;cACX2G,KAAK,EAAEzI,OAAO,CAACE,QAAS;cACxBwI,QAAQ,EAAG1E,CAAC,IAAK/D,UAAU,CAAC;gBAAC,GAAGD,OAAO;gBAAEE,QAAQ,EAAE8D,CAAC,CAAC+D,MAAM,CAACU;cAAK,CAAC,CAAE;cACpEE,QAAQ;YAAA;cAAA3D,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACT,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACC,CAAC,eACN/H,OAAA;YAAK0H,SAAS,EAAC,YAAY;YAAAC,QAAA,gBACzB3H,OAAA;cAAA2H,QAAA,EAAO;YAAU;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAO,CAAC,eACzB/H,OAAA;cACE0E,IAAI,EAAC,UAAU;cACf2G,KAAK,EAAEzI,OAAO,CAACG,QAAS;cACxBuI,QAAQ,EAAG1E,CAAC,IAAK/D,UAAU,CAAC;gBAAC,GAAGD,OAAO;gBAAEG,QAAQ,EAAE6D,CAAC,CAAC+D,MAAM,CAACU;cAAK,CAAC,CAAE;cACpEE,QAAQ;YAAA;cAAA3D,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACT,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACC,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH,CAAC,eACN/H,OAAA;UAAK0H,SAAS,EAAC,UAAU;UAAAC,QAAA,gBACvB3H,OAAA;YAAK0H,SAAS,EAAC,YAAY;YAAAC,QAAA,gBACzB3H,OAAA;cAAA2H,QAAA,EAAO;YAAW;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAO,CAAC,eAC1B/H,OAAA;cACE0E,IAAI,EAAC,MAAM;cACX2G,KAAK,EAAEzI,OAAO,CAACT,IAAK;cACpBmJ,QAAQ,EAAG1E,CAAC,IAAK/D,UAAU,CAAC;gBAAC,GAAGD,OAAO;gBAAET,IAAI,EAAEyE,CAAC,CAAC+D,MAAM,CAACU;cAAK,CAAC,CAAE;cAChEE,QAAQ;YAAA;cAAA3D,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACT,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACC,CAAC,eACN/H,OAAA;YAAK0H,SAAS,EAAC,YAAY;YAAAC,QAAA,gBACzB3H,OAAA;cAAA2H,QAAA,EAAO;YAAO;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAO,CAAC,eACtB/H,OAAA;cACE0E,IAAI,EAAC,OAAO;cACZ2G,KAAK,EAAEzI,OAAO,CAACI,KAAM;cACrBsI,QAAQ,EAAG1E,CAAC,IAAK/D,UAAU,CAAC;gBAAC,GAAGD,OAAO;gBAAEI,KAAK,EAAE4D,CAAC,CAAC+D,MAAM,CAACU;cAAK,CAAC,CAAE;cACjEE,QAAQ;YAAA;cAAA3D,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACT,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACC,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH,CAAC,eACN/H,OAAA;UAAK0H,SAAS,EAAC,YAAY;UAAAC,QAAA,gBACzB3H,OAAA;YAAA2H,QAAA,EAAO;UAAM;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAO,CAAC,eACrB/H,OAAA;YACEqL,KAAK,EAAEzI,OAAO,CAACK,IAAK;YACpBqI,QAAQ,EAAG1E,CAAC,IAAK/D,UAAU,CAAC;cAAC,GAAGD,OAAO;cAAEK,IAAI,EAAE2D,CAAC,CAAC+D,MAAM,CAACU;YAAK,CAAC,CAAE;YAAA1D,QAAA,gBAEhE3H,OAAA;cAAQqL,KAAK,EAAC,eAAe;cAAA1D,QAAA,EAAC;YAAa;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAQ,CAAC,eACpD/H,OAAA;cAAQqL,KAAK,EAAC,SAAS;cAAA1D,QAAA,EAAC;YAAO;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAQ,CAAC,eACxC/H,OAAA;cAAQqL,KAAK,EAAC,OAAO;cAAA1D,QAAA,EAAC;YAAK;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAQ,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAC9B,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACN,CAAC,eACN/H,OAAA;UAAK0H,SAAS,EAAC,cAAc;UAAAC,QAAA,gBAC3B3H,OAAA;YAAQ0E,IAAI,EAAC,QAAQ;YAACgD,SAAS,EAAC,UAAU;YAAAC,QAAA,EAAC;UAAW;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAQ,CAAC,eAC/D/H,OAAA;YAAQ0E,IAAI,EAAC,QAAQ;YAAC+E,OAAO,EAAEA,CAAA,KAAMtG,kBAAkB,CAAC,KAAK,CAAE;YAACuE,SAAS,EAAC,YAAY;YAAAC,QAAA,EAAC;UAAM;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAQ,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACnG,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACF,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACJ,CACN,eAED/H,OAAA;MAAK0H,SAAS,EAAC,YAAY;MAAAC,QAAA,EACxBpG,KAAK,CAAC4C,GAAG,CAACsI,QAAQ,iBACjBzM,OAAA;QAAwB0H,SAAS,EAAC,WAAW;QAAAC,QAAA,gBAC3C3H,OAAA;UAAK0H,SAAS,EAAC,aAAa;UAAAC,QAAA,gBAC1B3H,OAAA;YAAA2H,QAAA,EAAK8E,QAAQ,CAACtK;UAAI;YAAAyF,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAK,CAAC,eACxB/H,OAAA;YAAM0H,SAAS,EAAE,aAAa+E,QAAQ,CAACxJ,IAAI,EAAG;YAAA0E,QAAA,GAC3C8E,QAAQ,CAACxJ,IAAI,KAAK,OAAO,IAAI,aAAa,EAC1CwJ,QAAQ,CAACxJ,IAAI,KAAK,SAAS,IAAI,eAAe,EAC9CwJ,QAAQ,CAACxJ,IAAI,KAAK,eAAe,IAAI,qBAAqB;UAAA;YAAA2E,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACvD,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACJ,CAAC,eACN/H,OAAA;UAAK0H,SAAS,EAAC,cAAc;UAAAC,QAAA,gBAC3B3H,OAAA;YAAA2H,QAAA,gBAAG3H,OAAA;cAAA2H,QAAA,EAAQ;YAAS;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAQ,CAAC,KAAC,EAAC0E,QAAQ,CAAC3J,QAAQ;UAAA;YAAA8E,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAI,CAAC,eACrD/H,OAAA;YAAA2H,QAAA,gBAAG3H,OAAA;cAAA2H,QAAA,EAAQ;YAAM;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAQ,CAAC,KAAC,EAAC0E,QAAQ,CAACzJ,KAAK;UAAA;YAAA4E,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAI,CAAC,eAC/C/H,OAAA;YAAA2H,QAAA,gBAAG3H,OAAA;cAAA2H,QAAA,EAAQ;YAAO;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAQ,CAAC,KAAC,EAAC0E,QAAQ,CAACC,QAAQ,GAAG,UAAU,GAAG,YAAY;UAAA;YAAA9E,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAI,CAAC,eAC/E/H,OAAA;YAAA2H,QAAA,gBAAG3H,OAAA;cAAA2H,QAAA,EAAQ;YAAQ;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAQ,CAAC,KAAC,EAAC,IAAIvD,IAAI,CAACiI,QAAQ,CAACnE,SAAS,CAAC,CAACqE,kBAAkB,CAAC,CAAC;UAAA;YAAA/E,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAI,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACjF,CAAC,eACN/H,OAAA;UAAK0H,SAAS,EAAC,cAAc;UAAAC,QAAA,gBAC3B3H,OAAA;YACE0H,SAAS,EAAC,UAAU;YACpB+B,OAAO,EAAEA,CAAA,KAAM9G,cAAc,CAAC8J,QAAQ,CAACrI,GAAG,CAAE;YAAAuD,QAAA,EAC7C;UAED;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAQ,CAAC,eACT/H,OAAA;YACE0H,SAAS,EAAC,YAAY;YACtB+B,OAAO,EAAEA,CAAA,KAAMjC,gBAAgB,CAACiF,QAAQ,CAACrI,GAAG,CAAE;YAC9CsF,QAAQ,EAAE+C,QAAQ,CAACrI,GAAG,MAAK1D,IAAI,aAAJA,IAAI,uBAAJA,IAAI,CAAE0D,GAAG,CAAC;YAAAuD,QAAA,EACtC;UAED;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAQ,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACN,CAAC;MAAA,GA7BE0E,QAAQ,CAACrI,GAAG;QAAAwD,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OA8BjB,CACN;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACC,CAAC;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACH,CACN;EAED,MAAM6E,kBAAkB,GAAGA,CAAA,kBACzB5M,OAAA;IAAK0H,SAAS,EAAC,uBAAuB;IAAAC,QAAA,gBACpC3H,OAAA;MAAK0H,SAAS,EAAC,gBAAgB;MAAAC,QAAA,gBAC7B3H,OAAA;QAAA2H,QAAA,GAAI,2CAA+B,EAACtG,iBAAiB,CAACmE,MAAM,EAAC,GAAC;MAAA;QAAAoC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAI,CAAC,EAClE1G,iBAAiB,CAACmE,MAAM,KAAK,CAAC,iBAC7BxF,OAAA;QAAGgI,KAAK,EAAE;UAACC,KAAK,EAAE,MAAM;UAAEC,SAAS,EAAE;QAAQ,CAAE;QAAAP,QAAA,EAAC;MAAwB;QAAAC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAG,CAC5E;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACE,CAAC,EAEL1G,iBAAiB,CAAC8C,GAAG,CAACH,KAAK,iBAC1BhE,OAAA;MAAqB0H,SAAS,EAAC,mBAAmB;MAAAC,QAAA,gBAChD3H,OAAA;QAAK0H,SAAS,EAAC,cAAc;QAAAC,QAAA,gBAC3B3H,OAAA;UAAK0H,SAAS,EAAC,YAAY;UAAAC,QAAA,gBACzB3H,OAAA;YAAA2H,QAAA,GAAI,SAAO,EAAC3D,KAAK,CAACY,WAAW;UAAA;YAAAgD,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAK,CAAC,eACnC/H,OAAA;YAAK0H,SAAS,EAAC,YAAY;YAAAC,QAAA,gBACzB3H,OAAA;cAAM0H,SAAS,EAAC,YAAY;cAAAC,QAAA,GAAC,qBAAS,EAAC3D,KAAK,CAACM,KAAK,CAAC+D,WAAW;YAAA;cAAAT,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAO,CAAC,eACtE/H,OAAA;cAAM0H,SAAS,EAAC,YAAY;cAAAC,QAAA,GAAC,eAAG,EAAC,IAAInD,IAAI,CAACR,KAAK,CAACsE,SAAS,CAAC,CAACC,kBAAkB,CAAC,CAAC;YAAA;cAAAX,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAO,CAAC,eACvF/H,OAAA;cAAM0H,SAAS,EAAC,gBAAgB;cAAAC,QAAA,EAAC;YAAe;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAM,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACpD,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH,CAAC,eACN/H,OAAA;UAAK0H,SAAS,EAAC,aAAa;UAAAC,QAAA,eAC1B3H,OAAA;YAAA2H,QAAA,GAAQ,GAAC,EAAC3D,KAAK,CAAC+E,WAAW,CAACD,OAAO,CAAC,CAAC,CAAC;UAAA;YAAAlB,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAS;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAC7C,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH,CAAC,eAEN/H,OAAA;QAAK0H,SAAS,EAAC,kBAAkB;QAAAC,QAAA,gBAC/B3H,OAAA;UAAA2H,QAAA,gBAAG3H,OAAA;YAAA2H,QAAA,EAAQ;UAAS;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAQ,CAAC,KAAC,EAAC/D,KAAK,CAACgF,YAAY;QAAA;UAAApB,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAI,CAAC,eACtD/H,OAAA;UAAA2H,QAAA,gBAAG3H,OAAA;YAAA2H,QAAA,EAAQ;UAAM;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAQ,CAAC,KAAC,EAAC/D,KAAK,CAAC6I,aAAa;QAAA;UAAAjF,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAI,CAAC,EACnD/D,KAAK,CAACqF,mBAAmB,iBACxBrJ,OAAA;UAAA2H,QAAA,gBAAG3H,OAAA;YAAA2H,QAAA,EAAQ;UAAqB;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAQ,CAAC,KAAC,EAAC/D,KAAK,CAACqF,mBAAmB;QAAA;UAAAzB,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAI,CACzE;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACE,CAAC,eAEN/H,OAAA;QAAK0H,SAAS,EAAC,aAAa;QAAAC,QAAA,gBAC1B3H,OAAA;UAAA2H,QAAA,EAAI;QAAM;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAI,CAAC,EACd/D,KAAK,CAACwE,KAAK,CAACrE,GAAG,CAAC,CAACsE,IAAI,EAAEC,KAAK,kBAC3B1I,OAAA;UAAiB0H,SAAS,EAAC,YAAY;UAAAC,QAAA,gBACrC3H,OAAA;YAAA2H,QAAA,GAAOc,IAAI,CAACI,QAAQ,EAAC,IAAE,EAACJ,IAAI,CAACG,QAAQ,CAACzG,IAAI;UAAA;YAAAyF,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAO,CAAC,eAClD/H,OAAA;YAAA2H,QAAA,GAAM,GAAC,EAAC,CAACc,IAAI,CAACpG,KAAK,GAAGoG,IAAI,CAACI,QAAQ,EAAEC,OAAO,CAAC,CAAC,CAAC;UAAA;YAAAlB,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAO,CAAC;QAAA,GAF/CW,KAAK;UAAAd,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAGV,CACN,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACC,CAAC,eAEN/H,OAAA;QAAK0H,SAAS,EAAC,sBAAsB;QAAAC,QAAA,eACnC3H,OAAA;UACE0H,SAAS,EAAC,kBAAkB;UAC5B+B,OAAO,EAAEA,CAAA,KAAM/C,kBAAkB,CAAC1C,KAAK,CAACI,GAAG,CAAE;UAAAuD,QAAA,EAC9C;QAED;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAQ;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACN,CAAC;IAAA,GAxCE/D,KAAK,CAACI,GAAG;MAAAwD,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAyCd,CACN,CAAC;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACC,CACN;EAED,oBACE/H,OAAA;IAAK0H,SAAS,EAAC,iBAAiB;IAAAC,QAAA,gBAC9B3H,OAAA;MAAK0H,SAAS,EAAC,cAAc;MAAAC,QAAA,gBAC3B3H,OAAA;QAAK0H,SAAS,EAAC,aAAa;QAAAC,QAAA,gBAC1B3H,OAAA;UAAA2H,QAAA,EAAI;QAAqB;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAI,CAAC,eAC9B/H,OAAA;UAAA2H,QAAA,GAAG,WAAS,EAAC,CAAAjH,IAAI,aAAJA,IAAI,uBAAJA,IAAI,CAAEyB,IAAI,KAAI,eAAe;QAAA;UAAAyF,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAI,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAC5C,CAAC,eACN/H,OAAA;QAAK0H,SAAS,EAAC,cAAc;QAAAC,QAAA,gBAC3B3H,OAAA;UAAK0H,SAAS,EAAC,eAAe;UAAAC,QAAA,EAC3BhG,aAAa,CAACwC,GAAG,CAAC2I,YAAY,iBAC7B9M,OAAA;YAA2B0H,SAAS,EAAC,cAAc;YAAAC,QAAA,EAChDmF,YAAY,CAACnI;UAAO,GADbmI,YAAY,CAACvI,EAAE;YAAAqD,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAEpB,CACN;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACC,CAAC,eACN/H,OAAA;UAAQ0H,SAAS,EAAC,YAAY;UAAC+B,OAAO,EAAE9I,QAAS;UAAAgH,QAAA,EAAC;QAElD;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAQ,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACN,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH,CAAC,eAEN/H,OAAA;MAAK0H,SAAS,EAAC,YAAY;MAAAC,QAAA,gBACzB3H,OAAA;QACE0H,SAAS,EAAE7G,SAAS,KAAK,QAAQ,GAAG,QAAQ,GAAG,EAAG;QAClD4I,OAAO,EAAEA,CAAA,KAAM3I,YAAY,CAAC,QAAQ,CAAE;QAAA6G,QAAA,EACvC;MAED;QAAAC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAQ,CAAC,eACT/H,OAAA;QACE0H,SAAS,EAAE7G,SAAS,KAAK,eAAe,GAAG,QAAQ,GAAG,EAAG;QACzD4I,OAAO,EAAEA,CAAA,KAAM3I,YAAY,CAAC,eAAe,CAAE;QAAA6G,QAAA,GAC9C,gBACe,EAACtG,iBAAiB,CAACmE,MAAM,GAAG,CAAC,iBAAIxF,OAAA;UAAM0H,SAAS,EAAC,oBAAoB;UAAAC,QAAA,EAAEtG,iBAAiB,CAACmE;QAAM;UAAAoC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAO,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAC/G,CAAC,eACT/H,OAAA;QACE0H,SAAS,EAAE7G,SAAS,KAAK,QAAQ,GAAG,QAAQ,GAAG,EAAG;QAClD4I,OAAO,EAAEA,CAAA,KAAM3I,YAAY,CAAC,QAAQ,CAAE;QAAA6G,QAAA,EACvC;MAED;QAAAC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAQ,CAAC,eACT/H,OAAA;QACE0H,SAAS,EAAE7G,SAAS,KAAK,MAAM,GAAG,QAAQ,GAAG,EAAG;QAChD4I,OAAO,EAAEA,CAAA,KAAM3I,YAAY,CAAC,MAAM,CAAE;QAAA6G,QAAA,EACrC;MAED;QAAAC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAQ,CAAC,eACT/H,OAAA;QACE0H,SAAS,EAAE7G,SAAS,KAAK,OAAO,GAAG,QAAQ,GAAG,EAAG;QACjD4I,OAAO,EAAEA,CAAA,KAAM3I,YAAY,CAAC,OAAO,CAAE;QAAA6G,QAAA,EACtC;MAED;QAAAC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAQ,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACN,CAAC,eAEN/H,OAAA;MAAK0H,SAAS,EAAC,aAAa;MAAAC,QAAA,GACzB9G,SAAS,KAAK,QAAQ,IAAI4G,YAAY,CAAC,CAAC,EACxC5G,SAAS,KAAK,eAAe,IAAI+L,kBAAkB,CAAC,CAAC,EACrD/L,SAAS,KAAK,QAAQ,IAAI8I,YAAY,CAAC,CAAC,EACxC9I,SAAS,KAAK,MAAM,IAAIkK,oBAAoB,CAAC,CAAC,EAC9ClK,SAAS,KAAK,OAAO,IAAI2L,oBAAoB,CAAC,CAAC;IAAA;MAAA5E,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAC7C,CAAC;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACH,CAAC;AAEV,CAAC;AAACnH,EAAA,CA/6BIH,cAAc;AAAAsM,EAAA,GAAdtM,cAAc;AAi7BpB,eAAeA,cAAc;AAAC,IAAAsM,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}